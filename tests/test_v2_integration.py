"""
Integration tests for Sensei AI Suite v2.0.

Comprehensive test suite covering:
- End-to-end API testing
- Model training and prediction
- Data pipeline validation
- Performance benchmarks
"""

import asyncio
import pytest
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient

# Add src to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from sensei.core.config import get_settings
from sensei.core.exceptions import ModelError, DataError
from sensei.api.v2.main import app
from sensei.api.v2.services import PredictionService
from sensei.api.v2.models import (
    ConversionRequest, ChannelRequest, NLPRequest,
    ConversionResponse, ChannelResponse, NLPResponse
)
from sensei.models.conversion_v2 import ConversionModelV2
from sensei.models.channel_v2 import ChannelModelV2
from sensei.models.nlp_v2 import NLPModelV2
from sensei.data.manager import BigQueryDataSource
from sensei.features.store import AdvancedFeatureStore


class TestAPIIntegration:
    """Test API endpoints and integration."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_prediction_service(self):
        """Mock prediction service."""
        service = Mock(spec=PredictionService)
        service.get_model_status.return_value = {
            "conversion_v2": "ready",
            "channel_v2": "ready",
            "nlp_v2": "ready"
        }
        service.get_uptime.return_value = 3600.0
        return service
    
    def test_health_endpoint(self, client, mock_prediction_service):
        """Test health check endpoint."""
        with patch('sensei.api.v2.main.get_prediction_service', return_value=mock_prediction_service):
            response = client.get("/health")
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] is True
            assert "status" in data
            assert "version" in data
            assert "uptime_seconds" in data
            assert "models_status" in data
    
    def test_conversion_prediction_endpoint(self, client):
        """Test conversion prediction endpoint."""
        request_data = {
            "prospect_id": "test_prospect_123",
            "company_size": "medium",
            "industry": "technology",
            "nb_interactions": 5,
            "nb_calls": 2,
            "nb_emails": 8,
            "engagement_score": 0.7,
            "declared_budget": "medium",
            "days_in_pipeline": 30
        }
        
        # Mock the prediction service
        mock_response = ConversionResponse(
            prospect_id="test_prospect_123",
            conversion_probability=0.75,
            conversion_prediction=1,
            confidence_score=0.85,
            risk_factors=["Low interaction count"],
            opportunities=["High engagement score"],
            feature_importance={"engagement_score": 0.3, "nb_interactions": 0.2}
        )
        
        with patch('sensei.api.v2.main.get_prediction_service') as mock_service:
            mock_service.return_value.predict_conversion = AsyncMock(return_value=mock_response)
            
            response = client.post("/predict/conversion", json=request_data)
            assert response.status_code == 200
            
            data = response.json()
            assert data["prospect_id"] == "test_prospect_123"
            assert "conversion_probability" in data
            assert "confidence_score" in data
    
    def test_channel_optimization_endpoint(self, client):
        """Test channel optimization endpoint."""
        request_data = {
            "prospect_id": "test_prospect_456",
            "has_email": True,
            "has_phone": True,
            "email_response_rate": 0.15,
            "phone_response_rate": 0.05,
            "preferred_channel": "email",
            "urgency_level": 0.6
        }
        
        mock_response = ChannelResponse(
            prospect_id="test_prospect_456",
            optimal_channel="email",
            optimal_timing="afternoon",
            confidence_score=0.8,
            alternative_channels=[{"channel": "phone", "score": 0.6}],
            timing_recommendations={"morning": 0.7, "afternoon": 0.8},
            reasoning=["Email has higher response rate"]
        )
        
        with patch('sensei.api.v2.main.get_prediction_service') as mock_service:
            mock_service.return_value.optimize_channel = AsyncMock(return_value=mock_response)
            
            response = client.post("/predict/channel", json=request_data)
            assert response.status_code == 200
            
            data = response.json()
            assert data["prospect_id"] == "test_prospect_456"
            assert "optimal_channel" in data
            assert "optimal_timing" in data
    
    def test_nlp_analysis_endpoint(self, client):
        """Test NLP analysis endpoint."""
        request_data = {
            "content": "Bonjour, je suis très intéressé par votre solution. Pouvez-vous me donner plus d'informations ?",
            "language": "fr",
            "analyze_sentiment": True,
            "analyze_intent": True,
            "analyze_topics": True
        }
        
        mock_response = NLPResponse(
            content_length=len(request_data["content"]),
            language_detected="fr",
            sentiment={
                "label": "POSITIVE",
                "score": 0.85
            },
            intent={
                "intent": "request",
                "confidence": 0.9,
                "entities": []
            },
            topic={
                "topic_id": 1,
                "topic_name": "product_inquiry",
                "confidence": 0.8,
                "keywords": ["solution", "information"]
            },
            urgency_score=0.6,
            keywords=["solution", "information", "intéressé"],
            text_quality_score=0.9
        )
        
        with patch('sensei.api.v2.main.get_prediction_service') as mock_service:
            mock_service.return_value.analyze_text = AsyncMock(return_value=mock_response)
            
            response = client.post("/predict/nlp", json=request_data)
            assert response.status_code == 200
            
            data = response.json()
            assert "sentiment" in data
            assert "intent" in data
            assert "topic" in data
    
    def test_batch_processing_endpoint(self, client):
        """Test batch processing endpoint."""
        request_data = {
            "items": [
                {
                    "prospect_id": "batch_prospect_1",
                    "company_size": "small",
                    "nb_interactions": 3
                },
                {
                    "prospect_id": "batch_prospect_2",
                    "company_size": "large",
                    "nb_interactions": 10
                }
            ]
        }
        
        with patch('sensei.api.v2.main.get_prediction_service') as mock_service:
            # Mock individual predictions
            mock_service.return_value.predict_conversion = AsyncMock(
                side_effect=[
                    ConversionResponse(
                        prospect_id="batch_prospect_1",
                        conversion_probability=0.3,
                        conversion_prediction=0,
                        confidence_score=0.7
                    ),
                    ConversionResponse(
                        prospect_id="batch_prospect_2",
                        conversion_probability=0.8,
                        conversion_prediction=1,
                        confidence_score=0.9
                    )
                ]
            )
            
            response = client.post("/batch/conversion", json=request_data)
            assert response.status_code == 200
            
            data = response.json()
            assert data["total_items"] == 2
            assert data["successful_items"] == 2
            assert len(data["results"]) == 2


class TestModelIntegration:
    """Test model training and prediction integration."""
    
    @pytest.fixture
    def sample_conversion_data(self):
        """Create sample conversion training data."""
        np.random.seed(42)
        n_samples = 1000
        
        return pd.DataFrame({
            'prospect_id': [f"prospect_{i}" for i in range(n_samples)],
            'company_size': np.random.choice(['startup', 'small', 'medium', 'large'], n_samples),
            'industry': np.random.choice(['technology', 'finance', 'healthcare'], n_samples),
            'nb_interactions': np.random.poisson(5, n_samples),
            'nb_calls': np.random.poisson(2, n_samples),
            'nb_emails': np.random.poisson(8, n_samples),
            'engagement_score': np.random.beta(2, 5, n_samples),
            'days_in_pipeline': np.random.exponential(30, n_samples).astype(int),
            'target': np.random.binomial(1, 0.15, n_samples)
        })
    
    @pytest.fixture
    def sample_channel_data(self):
        """Create sample channel training data."""
        np.random.seed(42)
        n_samples = 1000
        
        return pd.DataFrame({
            'prospect_id': [f"prospect_{i}" for i in range(n_samples)],
            'has_email': np.random.choice([True, False], n_samples, p=[0.95, 0.05]),
            'has_phone': np.random.choice([True, False], n_samples, p=[0.8, 0.2]),
            'email_response_rate': np.random.beta(2, 8, n_samples),
            'phone_response_rate': np.random.beta(1, 19, n_samples),
            'urgency_level': np.random.beta(2, 3, n_samples),
            'optimal_channel': np.random.choice(['email', 'phone', 'meeting'], n_samples),
            'optimal_timing': np.random.choice(['morning', 'afternoon', 'evening'], n_samples)
        })
    
    @pytest.fixture
    def sample_nlp_data(self):
        """Create sample NLP training data."""
        sample_texts = [
            "Bonjour, je suis intéressé par votre solution.",
            "Pouvez-vous me donner plus d'informations sur les prix ?",
            "C'est urgent, j'ai besoin d'une réponse rapidement.",
            "Merci pour votre présentation, c'était très intéressant.",
            "Je ne suis pas sûr que cela corresponde à nos besoins."
        ] * 200  # 1000 samples
        
        return pd.DataFrame({
            'call_id': [f"call_{i}" for i in range(1000)],
            'content': sample_texts,
            'language': ['fr'] * 1000
        })
    
    def test_conversion_model_training(self, sample_conversion_data):
        """Test conversion model training."""
        model = ConversionModelV2()
        
        # Prepare data
        X = sample_conversion_data.drop(['prospect_id', 'target'], axis=1)
        y = sample_conversion_data['target']
        
        # Train model
        metrics = model.train(X, y, optimize_hyperparams=False)
        
        assert model.is_trained
        assert metrics is not None
        assert hasattr(metrics, 'accuracy')
        assert 0 <= metrics.accuracy <= 1
    
    def test_conversion_model_prediction(self, sample_conversion_data):
        """Test conversion model prediction."""
        model = ConversionModelV2()
        
        # Train model
        X = sample_conversion_data.drop(['prospect_id', 'target'], axis=1)
        y = sample_conversion_data['target']
        model.train(X, y, optimize_hyperparams=False)
        
        # Make predictions
        test_data = X.head(10)
        predictions = model.predict(test_data)
        probabilities = model.predict_proba(test_data)
        
        assert len(predictions) == 10
        assert len(probabilities) == 10
        assert all(p in [0, 1] for p in predictions)
        assert all(0 <= prob[1] <= 1 for prob in probabilities)
    
    def test_channel_model_training(self, sample_channel_data):
        """Test channel model training."""
        model = ChannelModelV2(use_deep_learning=False)  # Use traditional ML for testing
        
        # Prepare data
        X = sample_channel_data.drop(['prospect_id', 'optimal_channel', 'optimal_timing'], axis=1)
        y = sample_channel_data[['optimal_channel', 'optimal_timing']]
        
        # Train model
        metrics = model.train(X, y, optimize_hyperparams=False)
        
        assert model.is_trained
        assert metrics is not None
    
    def test_nlp_model_training(self, sample_nlp_data):
        """Test NLP model training."""
        model = NLPModelV2()
        
        # Train model (unsupervised)
        metrics = model.train(sample_nlp_data)
        
        assert model.is_trained
        assert metrics is not None
    
    def test_nlp_model_prediction(self, sample_nlp_data):
        """Test NLP model prediction."""
        model = NLPModelV2()
        
        # Train model
        model.train(sample_nlp_data)
        
        # Make predictions
        test_data = sample_nlp_data.head(5)
        results = model.predict(test_data)
        
        assert 'sentiment' in results
        assert 'intent' in results
        assert len(results['sentiment']) == 5
        assert len(results['intent']) == 5


class TestDataPipelineIntegration:
    """Test data pipeline and feature store integration."""
    
    @pytest.fixture
    def mock_bigquery_client(self):
        """Mock BigQuery client."""
        client = Mock()
        client.query.return_value.to_dataframe.return_value = pd.DataFrame({
            'prospect_id': ['test_1', 'test_2'],
            'feature_1': [1.0, 2.0],
            'feature_2': ['A', 'B']
        })
        return client
    
    def test_data_source_connection(self, mock_bigquery_client):
        """Test data source connection."""
        with patch('sensei.data.manager.bigquery.Client', return_value=mock_bigquery_client):
            data_source = BigQueryDataSource()
            data_source.connect()
            
            assert data_source.client is not None
    
    def test_data_source_query(self, mock_bigquery_client):
        """Test data source query execution."""
        with patch('sensei.data.manager.bigquery.Client', return_value=mock_bigquery_client):
            data_source = BigQueryDataSource()
            data_source.connect()
            
            query = "SELECT * FROM test_table LIMIT 10"
            df = data_source.query(query)
            
            assert not df.empty
            assert 'prospect_id' in df.columns
    
    def test_feature_store_operations(self):
        """Test feature store operations."""
        # Mock data source
        mock_data_source = Mock()
        mock_data_source.query.return_value = pd.DataFrame({
            'entity_id': ['entity_1', 'entity_2'],
            'feature_1': [1.0, 2.0],
            'feature_2': [0.5, 0.8]
        })
        
        feature_store = AdvancedFeatureStore(mock_data_source)
        
        # Test feature retrieval
        features = feature_store.get_features(
            feature_names=['feature_1', 'feature_2'],
            entity_ids=['entity_1', 'entity_2']
        )
        
        assert not features.empty
        assert 'entity_id' in features.columns
    
    def test_feature_drift_detection(self):
        """Test feature drift detection."""
        feature_store = AdvancedFeatureStore()
        
        # Create reference and current data
        np.random.seed(42)
        reference_data = pd.DataFrame({
            'feature_1': np.random.normal(0, 1, 1000),
            'feature_2': np.random.beta(2, 5, 1000)
        })
        
        # Current data with slight drift
        current_data = pd.DataFrame({
            'feature_1': np.random.normal(0.2, 1.1, 1000),  # Slight mean and variance shift
            'feature_2': np.random.beta(2.5, 5, 1000)  # Slight parameter shift
        })
        
        drift_reports = feature_store.detect_drift(reference_data, current_data)
        
        assert 'feature_1' in drift_reports
        assert 'feature_2' in drift_reports
        assert all(hasattr(report, 'drift_score') for report in drift_reports.values())


class TestPerformanceBenchmarks:
    """Performance benchmark tests."""
    
    @pytest.mark.asyncio
    async def test_api_response_time(self):
        """Test API response time benchmarks."""
        client = TestClient(app)
        
        # Mock prediction service
        mock_service = Mock()
        mock_service.predict_conversion = AsyncMock(
            return_value=ConversionResponse(
                prospect_id="perf_test",
                conversion_probability=0.5,
                conversion_prediction=1,
                confidence_score=0.8
            )
        )
        
        with patch('sensei.api.v2.main.get_prediction_service', return_value=mock_service):
            # Measure response time
            import time
            start_time = time.time()
            
            response = client.post("/predict/conversion", json={
                "prospect_id": "perf_test",
                "nb_interactions": 5
            })
            
            end_time = time.time()
            response_time = end_time - start_time
            
            assert response.status_code == 200
            assert response_time < 1.0  # Should respond within 1 second
    
    def test_model_prediction_performance(self, sample_conversion_data):
        """Test model prediction performance."""
        model = ConversionModelV2()
        
        # Train model
        X = sample_conversion_data.drop(['prospect_id', 'target'], axis=1)
        y = sample_conversion_data['target']
        model.train(X, y, optimize_hyperparams=False)
        
        # Measure prediction time
        test_data = X.head(100)
        
        import time
        start_time = time.time()
        predictions = model.predict(test_data)
        end_time = time.time()
        
        prediction_time = end_time - start_time
        
        assert len(predictions) == 100
        assert prediction_time < 0.1  # Should predict 100 samples in < 100ms


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
