["test_validation.py::test_config_import", "test_validation.py::test_data_validation", "test_validation.py::test_docker_file_validity", "test_validation.py::test_environment_variables", "test_validation.py::test_logging_functionality", "test_validation.py::test_model_metrics_validation", "test_validation.py::test_model_registry", "test_validation.py::test_model_training_pipeline", "test_validation.py::test_project_structure", "test_validation.py::test_required_files", "test_validation.py::test_requirements_compatibility", "test_validation.py::test_simple_training_models", "tests/integration/test_feature_store.py::TestFeatureStoreIntegration::test_build_features_daily_error_handling", "tests/integration/test_feature_store.py::TestFeatureStoreIntegration::test_build_features_daily_success", "tests/integration/test_feature_store.py::TestFeatureStoreIntegration::test_build_features_for_date_utility", "tests/integration/test_feature_store.py::TestFeatureStoreIntegration::test_create_audit_table", "tests/integration/test_feature_store.py::TestFeatureStoreIntegration::test_create_dataset_if_not_exists", "tests/integration/test_feature_store.py::TestFeatureStoreIntegration::test_feature_store_builder_initialization", "tests/integration/test_feature_store.py::TestFeatureStoreIntegration::test_feature_store_with_real_date_logic", "tests/integration/test_feature_store.py::TestFeatureStoreIntegration::test_render_sql_template", "tests/integration/test_feature_store.py::TestFeatureStoreIntegration::test_setup_ml_dataset_complete", "tests/integration/test_feature_store.py::TestFeatureStoreIntegration::test_sql_template_variables_injection", "tests/unit/test_data.py::TestSecretManager::test_get_bigquery_credentials", "tests/unit/test_data.py::TestSecretManager::test_get_secret_error", "tests/unit/test_data.py::TestSecretManager::test_get_secret_success", "tests/unit/test_data.py::TestSecretManager::test_global_get_secret_function", "tests/unit/test_data.py::TestSecretManager::test_initialization", "tests/unit/test_data.py::TestSecretManager::test_initialization_without_project_id", "tests/unit/test_data.py::TestSecureBigQueryClient::test_execute_query_validation", "tests/unit/test_data.py::TestSecureBigQueryClient::test_initialization_with_credentials_path", "tests/unit/test_data.py::TestSecureBigQueryClient::test_initialization_with_secret_manager", "tests/unit/test_data.py::TestSecureBigQueryClient::test_query_df_success", "tests/unit/test_data.py::TestSecureBigQueryClient::test_query_df_with_params", "tests/unit/test_data.py::TestSecureBigQueryClient::test_validate_query_permissions_forbidden_operations", "tests/unit/test_data.py::TestSecureBigQueryClient::test_validate_query_permissions_read_allowed", "tests/unit/test_data.py::TestSecureBigQueryClient::test_validate_query_permissions_write_to_ml_dataset", "tests/unit/test_models.py::TestBaseModel::test_base_model_initialization", "tests/unit/test_models.py::TestBaseModel::test_get_model_class", "tests/unit/test_models.py::TestBaseModel::test_register_model_decorator", "tests/unit/test_models.py::TestChannelModel::test_create_channel_timing_target", "tests/unit/test_models.py::TestChannelModel::test_initialization", "tests/unit/test_models.py::TestChannelModel::test_predict_channel_timing", "tests/unit/test_models.py::TestChannelModel::test_train", "tests/unit/test_models.py::TestConversionModel::test_identify_feature_types", "tests/unit/test_models.py::TestConversionModel::test_initialization", "tests/unit/test_models.py::TestConversionModel::test_predict_without_training", "tests/unit/test_models.py::TestConversionModel::test_prepare_features", "tests/unit/test_models.py::TestConversionModel::test_train", "tests/unit/test_models.py::TestNlpSignalsModel::test_get_cluster_analysis", "tests/unit/test_models.py::TestNlpSignalsModel::test_initialization", "tests/unit/test_models.py::TestNlpSignalsModel::test_prepare_features", "tests/unit/test_models.py::TestNlpSignalsModel::test_preprocess_text", "tests/unit/test_models.py::TestNlpSignalsModel::test_train"]