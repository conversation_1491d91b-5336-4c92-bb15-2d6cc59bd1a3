#!/usr/bin/env python3
"""
Complete pipeline test for Sensei AI Suite.

Tests the entire pipeline from training to API serving.
"""

import json
import time
import requests
import subprocess
import sys
from pathlib import Path
from datetime import datetime


def log_info(message: str, **kwargs):
    """Simple logging function."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    extra = " ".join([f"{k}={v}" for k, v in kwargs.items()])
    print(f"[{timestamp}] INFO: {message} {extra}")


def log_error(message: str, **kwargs):
    """Simple error logging function."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    extra = " ".join([f"{k}={v}" for k, v in kwargs.items()])
    print(f"[{timestamp}] ERROR: {message} {extra}")


def test_training_pipeline():
    """Test the training pipeline."""
    log_info("Testing training pipeline")
    
    try:
        # Test simple training
        result = subprocess.run([
            sys.executable, "train_simple.py", "--samples", "100"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode != 0:
            log_error("Simple training failed", stderr=result.stderr)
            return False
        
        log_info("Simple training: PASSED")
        
        # Test production training
        result = subprocess.run([
            sys.executable, "train_production.py", 
            "--capacity", "small", "--max-samples", "500"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode != 0:
            log_error("Production training failed", stderr=result.stderr)
            return False
        
        log_info("Production training: PASSED")
        return True
        
    except Exception as e:
        log_error(f"Training pipeline test failed: {e}")
        return False


def test_model_registry():
    """Test model registry functionality."""
    log_info("Testing model registry")
    
    try:
        registry_path = Path("models/registry.json")
        if not registry_path.exists():
            log_error("Model registry not found")
            return False
        
        with open(registry_path, 'r') as f:
            registry = json.load(f)
        
        # Check for required models
        required_models = ["conversion", "channel"]
        for model_name in required_models:
            if model_name not in registry:
                log_error(f"Model {model_name} not found in registry")
                return False
            
            # Check latest entry
            if not isinstance(registry[model_name], list) or not registry[model_name]:
                log_error(f"No entries for model {model_name}")
                return False
            
            latest = registry[model_name][-1]
            required_fields = ["version", "metrics", "created_at"]
            for field in required_fields:
                if field not in latest:
                    log_error(f"Missing field {field} in {model_name}")
                    return False
        
        log_info("Model registry: PASSED")
        return True
        
    except Exception as e:
        log_error(f"Model registry test failed: {e}")
        return False


def test_api_endpoints():
    """Test API endpoints."""
    log_info("Testing API endpoints")
    
    base_url = "http://localhost:8888"
    
    try:
        # Test health endpoint
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code != 200:
            log_error("Health endpoint failed", status=response.status_code)
            return False
        
        health_data = response.json()
        if health_data.get("status") != "healthy":
            log_error("API not healthy", status=health_data.get("status"))
            return False
        
        log_info("Health endpoint: PASSED")
        
        # Test conversion prediction
        conversion_data = {
            "prospect_id": "TEST_001",
            "nb_interactions": 5,
            "response_speed": "fast",
            "declared_budget": "large",
            "activity_sector": "tech"
        }
        
        response = requests.post(
            f"{base_url}/predict/conversion",
            json=conversion_data,
            timeout=5
        )
        
        if response.status_code != 200:
            log_error("Conversion prediction failed", status=response.status_code)
            return False
        
        pred_data = response.json()
        required_fields = ["prediction", "confidence", "model_version", "timestamp"]
        for field in required_fields:
            if field not in pred_data:
                log_error(f"Missing field {field} in conversion response")
                return False
        
        # Validate prediction range
        prediction = pred_data["prediction"]
        if not (0 <= prediction <= 1):
            log_error("Invalid prediction range", prediction=prediction)
            return False
        
        log_info("Conversion prediction: PASSED", prediction=prediction)
        
        # Test channel prediction
        channel_data = {
            "prospect_id": "TEST_002",
            "nb_appels_historique": 10,
            "has_phone": True,
            "has_email": True,
            "response_rate": 0.7
        }
        
        response = requests.post(
            f"{base_url}/predict/channel",
            json=channel_data,
            timeout=5
        )
        
        if response.status_code != 200:
            log_error("Channel prediction failed", status=response.status_code)
            return False
        
        channel_pred = response.json()
        if "prediction" not in channel_pred:
            log_error("Missing prediction in channel response")
            return False
        
        valid_channels = ["email_morning", "email_afternoon", "call_morning", "call_afternoon", "call_evening"]
        if channel_pred["prediction"] not in valid_channels:
            log_error("Invalid channel prediction", prediction=channel_pred["prediction"])
            return False
        
        log_info("Channel prediction: PASSED", prediction=channel_pred["prediction"])
        
        # Test models status
        response = requests.get(f"{base_url}/models/status", timeout=5)
        if response.status_code != 200:
            log_error("Models status failed", status=response.status_code)
            return False
        
        log_info("Models status: PASSED")
        
        return True
        
    except requests.exceptions.RequestException as e:
        log_error(f"API connection failed: {e}")
        return False
    except Exception as e:
        log_error(f"API test failed: {e}")
        return False


def test_performance():
    """Test API performance."""
    log_info("Testing API performance")
    
    base_url = "http://localhost:8888"
    
    try:
        # Test multiple requests
        times = []
        for i in range(10):
            start_time = time.time()
            
            response = requests.post(
                f"{base_url}/predict/conversion",
                json={
                    "prospect_id": f"PERF_TEST_{i}",
                    "nb_interactions": i + 1,
                    "response_speed": "medium",
                    "declared_budget": "medium",
                    "activity_sector": "tech"
                },
                timeout=5
            )
            
            if response.status_code != 200:
                log_error("Performance test request failed", request=i)
                return False
            
            elapsed = time.time() - start_time
            times.append(elapsed)
        
        # Calculate statistics
        avg_time = sum(times) / len(times)
        max_time = max(times)
        min_time = min(times)
        
        # Performance thresholds
        if avg_time > 1.0:  # 1 second average
            log_error("Average response time too high", avg_time=avg_time)
            return False
        
        if max_time > 2.0:  # 2 seconds max
            log_error("Max response time too high", max_time=max_time)
            return False
        
        log_info("Performance test: PASSED", 
                avg_time_ms=int(avg_time * 1000),
                max_time_ms=int(max_time * 1000),
                min_time_ms=int(min_time * 1000))
        
        return True
        
    except Exception as e:
        log_error(f"Performance test failed: {e}")
        return False


def test_validation_suite():
    """Run the validation test suite."""
    log_info("Running validation test suite")
    
    try:
        result = subprocess.run([
            sys.executable, "test_validation.py"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode != 0:
            log_error("Validation suite failed", stderr=result.stderr)
            return False
        
        log_info("Validation suite: PASSED")
        return True
        
    except Exception as e:
        log_error(f"Validation suite failed: {e}")
        return False


def main():
    """Run complete pipeline test."""
    print("="*80)
    print("SENSEI AI SUITE - COMPLETE PIPELINE TEST")
    print("="*80)
    
    tests = [
        ("Training Pipeline", test_training_pipeline),
        ("Model Registry", test_model_registry),
        ("Validation Suite", test_validation_suite),
        ("API Endpoints", test_api_endpoints),
        ("API Performance", test_performance)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        log_info(f"Starting {test_name}")
        try:
            success = test_func()
            results[test_name] = "PASSED" if success else "FAILED"
        except Exception as e:
            log_error(f"{test_name} crashed: {e}")
            results[test_name] = "CRASHED"
        
        print("-" * 80)
    
    # Print summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    all_passed = True
    for test_name, result in results.items():
        status_color = "✅" if result == "PASSED" else "❌"
        print(f"{status_color} {test_name:25} | {result}")
        if result != "PASSED":
            all_passed = False
    
    print("="*80)
    
    if all_passed:
        print("🎉 ALL TESTS PASSED - SENSEI AI SUITE IS READY FOR PRODUCTION!")
        return 0
    else:
        print("❌ SOME TESTS FAILED - PLEASE CHECK THE LOGS")
        return 1


if __name__ == "__main__":
    sys.exit(main())
