# Sensei AI Suite v2.0 - Production Requirements
# Next-generation ML platform for B2B sales intelligence

# Core Data Science & ML
numpy==1.24.3
pandas==2.1.0
scikit-learn==1.3.0
scipy==1.11.1

# Advanced ML Models
xgboost==1.7.6
lightgbm==4.0.0
catboost==1.2.2

# Deep Learning & NLP
torch==2.0.1+cpu
transformers==4.33.2
sentence-transformers==2.2.2
tokenizers==0.13.3

# Clustering & Dimensionality Reduction
umap-learn==0.5.3
hdbscan==0.8.29

# Hyperparameter Optimization
optuna==3.3.0
hyperopt==0.2.7

# API Framework
fastapi==0.104.1
uvicorn[standard]==0.23.2
pydantic==2.4.2
pydantic-settings==2.0.3

# Google Cloud Platform
google-cloud-bigquery==3.11.4
google-cloud-secret-manager==2.16.4
google-auth==2.23.0
google-cloud-storage==2.10.0

# Data Validation & Processing
marshmallow==3.20.1
cerberus==1.3.4

# Caching & Performance
redis==4.6.0
psutil==5.9.5

# Logging & Monitoring
structlog==23.1.0
prometheus-client==0.17.1

# Security
cryptography==41.0.4
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# Utilities
python-dotenv==1.0.0
click==8.1.7
tqdm==4.66.1
joblib==1.3.2
pyyaml==6.0.1

# Development & Testing
pytest==7.4.2
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.11.1
httpx==0.24.1

# Code Quality
black==23.7.0
isort==5.12.0
flake8==6.0.0
mypy==1.5.1
pre-commit==3.4.0

# Documentation
mkdocs==1.5.2
mkdocs-material==9.2.6

# Optional: Advanced NLP (comment out if not needed)
# spacy==3.6.1
# nltk==3.8.1

# Optional: Computer Vision (comment out if not needed)
# opencv-python==4.8.0.76
# pillow==10.0.0

# Optional: Time Series (comment out if not needed)
# prophet==1.1.4
# statsmodels==0.14.0

# Optional: Distributed Computing (comment out if not needed)
# dask[complete]==2023.8.1
# ray[default]==2.6.3
