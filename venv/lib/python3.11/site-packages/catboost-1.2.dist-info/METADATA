Metadata-Version: 2.1
Name: catboost
Version: 1.2
Summary: CatBoost Python Package
Home-page: https://catboost.ai
Author: CatBoost Developers
License: Apache License, Version 2.0
Project-URL: GitHub, https://github.com/catboost/catboost
Project-URL: Bug Tracker, https://github.com/catboost/catboost/issues
Project-URL: Documentation, https://catboost.ai/docs/concepts/about.html
Project-URL: Benchmarks, https://catboost.ai/#benchmark
Keywords: catboost
Platform: Linux
Platform: Mac OSX
Platform: Windows
Platform: Unix
Classifier: Development Status :: 5 - Production/Stable
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Dist: graphviz
Requires-Dist: matplotlib
Requires-Dist: numpy (>=1.16.0)
Requires-Dist: pandas (>=0.24)
Requires-Dist: scipy
Requires-Dist: plotly
Requires-Dist: six
Provides-Extra: widget
Requires-Dist: traitlets ; extra == 'widget'
Requires-Dist: ipython ; extra == 'widget'
Requires-Dist: ipywidgets (<9.0,>=7.0) ; extra == 'widget'

CatBoost is a fast, scalable, high performance gradient boosting on decision trees library. Used for ranking, classification, regression and other ML tasks.
