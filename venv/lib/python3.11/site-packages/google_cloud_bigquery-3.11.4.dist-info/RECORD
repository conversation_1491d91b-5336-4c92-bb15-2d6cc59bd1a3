google/cloud/bigquery/__init__.py,sha256=zFt5KL6rJXpTs8VANvvpVzuP83Y2C_tQvPYhuQGMTgA,7958
google/cloud/bigquery/__pycache__/__init__.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/_helpers.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/_http.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/_job_helpers.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/_pandas_helpers.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/_tqdm_helpers.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/client.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/dataset.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/encryption_configuration.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/enums.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/exceptions.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/external_config.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/format_options.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/iam.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/model.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/opentelemetry_tracing.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/query.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/retry.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/schema.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/standard_sql.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/table.cpython-311.pyc,,
google/cloud/bigquery/__pycache__/version.cpython-311.pyc,,
google/cloud/bigquery/_helpers.py,sha256=6Bz2Mj_1Kd2MNcys146aM7PPQkjX1XsGnRKyZ-OVbAk,31709
google/cloud/bigquery/_http.py,sha256=QL1Yny3KKQM1_PbUvC1hT4gr1cEVVk0tN45Hlw0pMlY,2039
google/cloud/bigquery/_job_helpers.py,sha256=A34j8mmvdkqTdNSskF_beus21SxZyJjl-yHeyZH4vDc,9466
google/cloud/bigquery/_pandas_helpers.py,sha256=Vb-aDmFOBRy8C5TmxOCInQzSDJXc-a3lNSbcnMypCxI,36601
google/cloud/bigquery/_tqdm_helpers.py,sha256=Rg2cHUATbw2-rL805llE-lAgq2qQ7X5czTxhtq7prmQ,4542
google/cloud/bigquery/client.py,sha256=YdhDAcxpWnmNJr9Zz8ZGIViquUVNtWfeYqlVCxzP25E,161011
google/cloud/bigquery/dataset.py,sha256=9yRflbwNoBhyIyK2UdKo3eaRa-1QrAnPtPNpGR3Frc0,31341
google/cloud/bigquery/dbapi/__init__.py,sha256=Q3j6V96x0ozjxDQdGfMYvg_cFAOK39yBq1OAnwse8Wo,2980
google/cloud/bigquery/dbapi/__pycache__/__init__.cpython-311.pyc,,
google/cloud/bigquery/dbapi/__pycache__/_helpers.cpython-311.pyc,,
google/cloud/bigquery/dbapi/__pycache__/connection.cpython-311.pyc,,
google/cloud/bigquery/dbapi/__pycache__/cursor.cpython-311.pyc,,
google/cloud/bigquery/dbapi/__pycache__/exceptions.cpython-311.pyc,,
google/cloud/bigquery/dbapi/__pycache__/types.cpython-311.pyc,,
google/cloud/bigquery/dbapi/_helpers.py,sha256=mDkYFXxFjxkPHJ7dCRVMAUAZ2Xe-zct_YwfYsnwY_Do,17000
google/cloud/bigquery/dbapi/connection.py,sha256=k2KAPbYMF_2WeXlYaZTcGRGJUq3UH_d7D70Q8DToAOc,4476
google/cloud/bigquery/dbapi/cursor.py,sha256=TuiyGKpEt8lvXrCsSJudBRygasFeC53Mywb-0jfomDc,20135
google/cloud/bigquery/dbapi/exceptions.py,sha256=xPTDFI2LCyRLKcKm4m11rCvrBd901tKrOjHQWi_MRV0,1685
google/cloud/bigquery/dbapi/types.py,sha256=_7fg9256shsJTOP6iaGT0-HYx2xXCZ46xZNbF6-oNXw,2769
google/cloud/bigquery/encryption_configuration.py,sha256=BB5hNwwxFy41saz1dyJ4QDBrQO3QBi2JRC3zGOnzJTc,2635
google/cloud/bigquery/enums.py,sha256=-lsyPhH57zO20Qiu4lU0io7z8kPqApbbLnhu6w5-Bng,9882
google/cloud/bigquery/exceptions.py,sha256=QZyxOIM86I5bQX-Foi1LWMOQLr6LVsYLey8fwDit0w0,954
google/cloud/bigquery/external_config.py,sha256=_uw1wSF8rIKWQgMzStSk13BfVVjwlsyxEj5kx0QltNg,33949
google/cloud/bigquery/format_options.py,sha256=aX4JBK3CXW9lKPz-oLjoKTkWM0xswnoewhfk9_Ouk8w,4402
google/cloud/bigquery/iam.py,sha256=w2d82vjPO78pQriflvU9Kd5CQVNNdvXu1Ykjj6ftOSc,1554
google/cloud/bigquery/job/__init__.py,sha256=F6tw1yEmdk6qOqLYT9hY4Vno4X3iaLXAJ97-3cWKXx0,3270
google/cloud/bigquery/job/__pycache__/__init__.cpython-311.pyc,,
google/cloud/bigquery/job/__pycache__/base.cpython-311.pyc,,
google/cloud/bigquery/job/__pycache__/copy_.cpython-311.pyc,,
google/cloud/bigquery/job/__pycache__/extract.cpython-311.pyc,,
google/cloud/bigquery/job/__pycache__/load.cpython-311.pyc,,
google/cloud/bigquery/job/__pycache__/query.cpython-311.pyc,,
google/cloud/bigquery/job/base.py,sha256=k8iUFw-hdzqK_yxHKHvH1y4sr8Q9v-tjI6bHMpToHX4,35855
google/cloud/bigquery/job/copy_.py,sha256=yCpkUDha5AxRztC4C8raejs8uq2x5YzDXTgcw52dyN0,10125
google/cloud/bigquery/job/extract.py,sha256=BF11RegU_b6TyEc-sz4CkAjnfvF4smvgyy80AQSBWcg,9573
google/cloud/bigquery/job/load.py,sha256=nnZFYr2LsK3Ho8agTCPcQuPIFIM2NExEk0WcxmGoeBQ,32445
google/cloud/bigquery/job/query.py,sha256=BEvA7C1ai-5piwnjvDdwW0JCJ0_nR4P2rUSZPWLAaoc,83821
google/cloud/bigquery/magics/__init__.py,sha256=ML6JJ9xOS0xWS-c6vT3BdC2RFXrzpuj9RDsgV7SDffw,775
google/cloud/bigquery/magics/__pycache__/__init__.cpython-311.pyc,,
google/cloud/bigquery/magics/__pycache__/magics.cpython-311.pyc,,
google/cloud/bigquery/magics/line_arg_parser/__init__.py,sha256=jMf9Dwa1uPkN5e5W36zcKQASYg6p9Da37-ZvhNKEWUI,1252
google/cloud/bigquery/magics/line_arg_parser/__pycache__/__init__.cpython-311.pyc,,
google/cloud/bigquery/magics/line_arg_parser/__pycache__/exceptions.cpython-311.pyc,,
google/cloud/bigquery/magics/line_arg_parser/__pycache__/lexer.cpython-311.pyc,,
google/cloud/bigquery/magics/line_arg_parser/__pycache__/parser.cpython-311.pyc,,
google/cloud/bigquery/magics/line_arg_parser/__pycache__/visitors.cpython-311.pyc,,
google/cloud/bigquery/magics/line_arg_parser/exceptions.py,sha256=TFEA8d8tJKJHvwo1mLiYF-CGDpFGow0fdekauw_KTQo,779
google/cloud/bigquery/magics/line_arg_parser/lexer.py,sha256=3OxoPuMFQJAqMlLyssOIW9KDgsLY1VLsCxTbP5lSWek,7948
google/cloud/bigquery/magics/line_arg_parser/parser.py,sha256=rwjCiivLeGEP2vfplWvxRsdFtLvxONLJvA8Z64JzOL0,15999
google/cloud/bigquery/magics/line_arg_parser/visitors.py,sha256=XXHk9R-3f97qwASVBEkVuxgu8NQSisEScs6Coi5gExo,5151
google/cloud/bigquery/magics/magics.py,sha256=v4YM8cqIryFcBX3gOqfLc053OfvOwrsGcB7JmqlcFps,28101
google/cloud/bigquery/model.py,sha256=s9J0aLkUGUguUR8QwLtnQ3GfBT4Ea6BmLqlAuqc9GP8,14826
google/cloud/bigquery/opentelemetry_tracing.py,sha256=qoBw5mOdgKWwOukajaMkyTGW6NvNMwK58h_V5XFnswQ,5735
google/cloud/bigquery/py.typed,sha256=6fUaZ4QqCRbkYNrMxSPvYN7P3PdkkNtPjyLdimHatBc,82
google/cloud/bigquery/query.py,sha256=aCT648TOwsz-WiM3_veNydJhlEB8y2ain8PYelpU6eI,34707
google/cloud/bigquery/retry.py,sha256=MF2tjrxoMZd0q9D09S6YKYIZcQWkmKKQZaHqIxGPUvY,2621
google/cloud/bigquery/routine/__init__.py,sha256=1_bQ2j78eB17B_MXJMbjr_ncjEG7crX3UVcd-wrE4Gk,1138
google/cloud/bigquery/routine/__pycache__/__init__.cpython-311.pyc,,
google/cloud/bigquery/routine/__pycache__/routine.cpython-311.pyc,,
google/cloud/bigquery/routine/routine.py,sha256=vv8ZCbQw3PbFyK9u9XndjdxawkAwsjAE6rZIx-PGOTY,23875
google/cloud/bigquery/schema.py,sha256=qTzKfNNhOOI9aD3bKfaIrNjaW_4D746DvKvAuE1V7ww,18623
google/cloud/bigquery/standard_sql.py,sha256=CHfFH4Uhpb403GK0peUeAgxy2Fr3HtWmAjIK5lCtJSo,12081
google/cloud/bigquery/table.py,sha256=f8FivSHciJRMCRTkkBb91CTOuv2w979rV2HKbobwffw,109712
google/cloud/bigquery/version.py,sha256=zlpEoiqaMaZjYtZ3PDvyF1pUotimgS7Fut1GaYxGn14,598
google/cloud/bigquery_v2/__init__.py,sha256=3fu-g0AXJpcodVM-NlklHSchgc7MvLNvL3cmIqwoUJ0,1782
google/cloud/bigquery_v2/__pycache__/__init__.cpython-311.pyc,,
google/cloud/bigquery_v2/types/__init__.py,sha256=CzvM5-4tpyAvrTJznzVlVoSeF91-LnRG7srS3_Fc2iY,1385
google/cloud/bigquery_v2/types/__pycache__/__init__.cpython-311.pyc,,
google/cloud/bigquery_v2/types/__pycache__/encryption_config.cpython-311.pyc,,
google/cloud/bigquery_v2/types/__pycache__/model.cpython-311.pyc,,
google/cloud/bigquery_v2/types/__pycache__/model_reference.cpython-311.pyc,,
google/cloud/bigquery_v2/types/__pycache__/standard_sql.cpython-311.pyc,,
google/cloud/bigquery_v2/types/__pycache__/table_reference.cpython-311.pyc,,
google/cloud/bigquery_v2/types/encryption_config.py,sha256=ToZ_7KNCeN9NEJ6qC46i3ITlGguw6KXvWaMTbpjwLTE,1399
google/cloud/bigquery_v2/types/model.py,sha256=B5juTZHBpywvyDZn0b4SfBtObvxvo240pEur4ooHUwo,75103
google/cloud/bigquery_v2/types/model_reference.py,sha256=LdV5ofA17ov0IVpw5_vqFWkjww_T2p8piccWW_6CWUE,1539
google/cloud/bigquery_v2/types/standard_sql.py,sha256=h9-2p1UMhnjJzpIk_6SYDJnsN5FbfawyzQKJvHa5KvQ,4428
google/cloud/bigquery_v2/types/table_reference.py,sha256=0jOHzgto1w86PYfru7oHqk_MBEpEqxKTYqQ6tfBpm1c,2509
google_cloud_bigquery-3.11.4-py3.9-nspkg.pth,sha256=b0D5dZk3RUzK54tZ9iZDvLm7u8ltc5EzYrGCmhsuoNw,1698
google_cloud_bigquery-3.11.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
google_cloud_bigquery-3.11.4.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
google_cloud_bigquery-3.11.4.dist-info/METADATA,sha256=FGGReYHZSYK9pCFgR9raxefhsL0r4dosDCx1dKSEtko,8475
google_cloud_bigquery-3.11.4.dist-info/RECORD,,
google_cloud_bigquery-3.11.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
google_cloud_bigquery-3.11.4.dist-info/WHEEL,sha256=bb2Ot9scclHKMOLDEHY6B2sicWOgugjFKaJsT7vwMQo,110
google_cloud_bigquery-3.11.4.dist-info/namespace_packages.txt,sha256=v8IaYqRE2a0onAGJIpZeFkkH83wXSWZRR9eOyfMwoTc,20
google_cloud_bigquery-3.11.4.dist-info/top_level.txt,sha256=_1QvSJIhFAGfxb79D6DhB7SUw2X6T4rwnz_LLrbcD3c,7
