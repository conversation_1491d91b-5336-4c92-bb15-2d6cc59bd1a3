# Sensei AI Suite v2.0 - Test Requirements (Simplified)

# Core Data Science & ML
numpy==1.24.3
pandas==2.1.0
scikit-learn==1.3.0
scipy==1.11.1

# Basic ML Models (simplified for testing)
xgboost
catboost

# API Framework
fastapi==0.104.1
uvicorn[standard]==0.23.2
pydantic==2.4.2
pydantic-settings==2.0.3

# Basic utilities
python-dotenv==1.0.0
click==8.1.7
tqdm==4.66.1
joblib==1.3.2
pyyaml==6.0.1

# Testing
pytest==7.4.2
pytest-asyncio==0.21.1
httpx==0.24.1

# Logging
structlog==23.1.0
