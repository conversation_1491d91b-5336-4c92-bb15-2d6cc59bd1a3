#!/bin/bash
# Sensei AI Suite v2.0 - Build Script

set -euo pipefail

# Configuration
VERSION=${VERSION:-2.0.0}
BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
VCS_REF=$(git rev-parse --short HEAD)
IMAGE_NAME="sensei-ai-v2"
REGISTRY=${REGISTRY:-"gcr.io/datalake-sensei"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check Git
    if ! command -v git &> /dev/null; then
        log_error "Git is not installed"
        exit 1
    fi
    
    # Check if we're in a git repository
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_error "Not in a git repository"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Validate code quality
validate_code() {
    log_info "Validating code quality..."
    
    # Run validation script
    if python validate_v2.py --quick; then
        log_success "Code validation passed"
    else
        log_error "Code validation failed"
        exit 1
    fi
}

# Run tests
run_tests() {
    log_info "Running tests..."
    
    if python -m pytest tests/test_v2_integration.py -v --tb=short; then
        log_success "Tests passed"
    else
        log_error "Tests failed"
        exit 1
    fi
}

# Build Docker image
build_image() {
    log_info "Building Docker image..."
    
    local full_image_name="${REGISTRY}/${IMAGE_NAME}:${VERSION}"
    local latest_image_name="${REGISTRY}/${IMAGE_NAME}:latest"
    
    # Build image
    docker build \
        --file Dockerfile \
        --target production \
        --build-arg BUILD_DATE="${BUILD_DATE}" \
        --build-arg VERSION="${VERSION}" \
        --build-arg VCS_REF="${VCS_REF}" \
        --tag "${full_image_name}" \
        --tag "${latest_image_name}" \
        .
    
    log_success "Docker image built: ${full_image_name}"
}

# Test Docker image
test_image() {
    log_info "Testing Docker image..."
    
    local image_name="${REGISTRY}/${IMAGE_NAME}:${VERSION}"
    
    # Run container for testing
    local container_id=$(docker run -d \
        --name "sensei-test-${VCS_REF}" \
        -p 8001:8000 \
        -e ENVIRONMENT=testing \
        "${image_name}")
    
    # Wait for container to start
    sleep 30
    
    # Test health endpoint
    if curl -f http://localhost:8001/health > /dev/null 2>&1; then
        log_success "Docker image test passed"
    else
        log_error "Docker image test failed"
        docker logs "${container_id}"
        docker stop "${container_id}" || true
        docker rm "${container_id}" || true
        exit 1
    fi
    
    # Cleanup
    docker stop "${container_id}" || true
    docker rm "${container_id}" || true
}

# Push image to registry
push_image() {
    log_info "Pushing image to registry..."
    
    local full_image_name="${REGISTRY}/${IMAGE_NAME}:${VERSION}"
    local latest_image_name="${REGISTRY}/${IMAGE_NAME}:latest"
    
    # Configure Docker for GCR
    if [[ "${REGISTRY}" == gcr.io/* ]]; then
        gcloud auth configure-docker --quiet
    fi
    
    # Push images
    docker push "${full_image_name}"
    docker push "${latest_image_name}"
    
    log_success "Images pushed to registry"
}

# Generate build report
generate_report() {
    log_info "Generating build report..."
    
    local report_file="build-report-${VERSION}-${VCS_REF}.json"
    
    cat > "${report_file}" << EOF
{
  "version": "${VERSION}",
  "build_date": "${BUILD_DATE}",
  "vcs_ref": "${VCS_REF}",
  "image_name": "${REGISTRY}/${IMAGE_NAME}:${VERSION}",
  "build_status": "success",
  "build_duration": "${SECONDS}s"
}
EOF
    
    log_success "Build report generated: ${report_file}"
}

# Main function
main() {
    log_info "Starting Sensei AI Suite v2.0 build process..."
    log_info "Version: ${VERSION}"
    log_info "Build Date: ${BUILD_DATE}"
    log_info "VCS Ref: ${VCS_REF}"
    
    check_prerequisites
    validate_code
    run_tests
    build_image
    test_image
    
    # Only push if PUSH_IMAGE is set to true
    if [[ "${PUSH_IMAGE:-false}" == "true" ]]; then
        push_image
    else
        log_warning "Skipping image push (set PUSH_IMAGE=true to enable)"
    fi
    
    generate_report
    
    log_success "Build completed successfully in ${SECONDS}s"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --version)
            VERSION="$2"
            shift 2
            ;;
        --push)
            PUSH_IMAGE=true
            shift
            ;;
        --registry)
            REGISTRY="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [--version VERSION] [--push] [--registry REGISTRY] [--help]"
            echo ""
            echo "Options:"
            echo "  --version VERSION    Set build version (default: 2.0.0)"
            echo "  --push              Push image to registry"
            echo "  --registry REGISTRY Set Docker registry (default: gcr.io/datalake-sensei)"
            echo "  --help              Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main "$@"
