# Prometheus configuration for Sensei AI Suite v2.0
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Sensei API metrics
  - job_name: 'sensei-api'
    static_configs:
      - targets: ['sensei-api:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Redis metrics (if available)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
