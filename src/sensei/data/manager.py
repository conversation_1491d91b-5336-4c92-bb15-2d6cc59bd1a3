"""
Advanced data management system for Sensei AI Suite v2.0.

Features:
- Unified data access layer
- Intelligent caching with Redis
- Query optimization and validation
- Data quality monitoring
- Schema validation and evolution
"""

import hashlib
import json
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
from pathlib import Path

import pandas as pd
import numpy as np
from pydantic import BaseModel, validator
import redis
from sqlalchemy import create_engine, text
from google.cloud import bigquery

from ..core.config import get_settings
from ..core.exceptions import DataError, DataQualityError, DataSchemaError
from ..core.interfaces import IDataSource
from ..core.logging import get_logger, log_performance


class DataQualityMetrics(BaseModel):
    """Data quality metrics."""
    completeness: float  # % of non-null values
    uniqueness: float    # % of unique values
    validity: float      # % of valid values
    consistency: float   # % of consistent values
    timeliness: float    # % of recent data
    accuracy: float      # % of accurate values (if ground truth available)


class DataSchema(BaseModel):
    """Data schema definition."""
    table_name: str
    columns: Dict[str, str]  # column_name -> data_type
    primary_key: List[str]
    foreign_keys: Dict[str, str]  # column -> referenced_table.column
    constraints: Dict[str, Any]
    created_at: datetime
    updated_at: datetime


class QueryMetadata(BaseModel):
    """Query execution metadata."""
    query_hash: str
    execution_time: float
    rows_returned: int
    bytes_processed: int
    cache_hit: bool
    timestamp: datetime


class CacheManager:
    """Redis-based cache manager."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        """Initialize cache manager."""
        self.redis_client = redis.from_url(redis_url)
        self.default_ttl = 3600  # 1 hour
        self.logger = get_logger(__name__)
    
    def _generate_key(self, query: str, params: Dict[str, Any] = None) -> str:
        """Generate cache key from query and parameters."""
        content = query + json.dumps(params or {}, sort_keys=True)
        return f"sensei:query:{hashlib.md5(content.encode()).hexdigest()}"
    
    def get(self, query: str, params: Dict[str, Any] = None) -> Optional[pd.DataFrame]:
        """Get cached query result."""
        try:
            key = self._generate_key(query, params)
            cached_data = self.redis_client.get(key)
            
            if cached_data:
                # Deserialize DataFrame
                data = json.loads(cached_data)
                df = pd.DataFrame(data['data'])
                
                # Restore data types
                for col, dtype in data['dtypes'].items():
                    if col in df.columns:
                        df[col] = df[col].astype(dtype)
                
                self.logger.debug("Cache hit", query_hash=key[:8])
                return df
                
        except Exception as e:
            self.logger.warning("Cache get failed", error=str(e))
        
        return None
    
    def set(
        self,
        query: str,
        df: pd.DataFrame,
        params: Dict[str, Any] = None,
        ttl: Optional[int] = None
    ) -> None:
        """Cache query result."""
        try:
            key = self._generate_key(query, params)
            ttl = ttl or self.default_ttl
            
            # Serialize DataFrame
            data = {
                'data': df.to_dict('records'),
                'dtypes': df.dtypes.astype(str).to_dict(),
                'cached_at': datetime.utcnow().isoformat()
            }
            
            self.redis_client.setex(
                key,
                ttl,
                json.dumps(data, default=str)
            )
            
            self.logger.debug("Cache set", query_hash=key[:8], ttl=ttl)
            
        except Exception as e:
            self.logger.warning("Cache set failed", error=str(e))
    
    def invalidate_pattern(self, pattern: str) -> int:
        """Invalidate cache entries matching pattern."""
        try:
            keys = self.redis_client.keys(f"sensei:query:*{pattern}*")
            if keys:
                return self.redis_client.delete(*keys)
            return 0
        except Exception as e:
            self.logger.warning("Cache invalidation failed", error=str(e))
            return 0


class DataQualityValidator:
    """Data quality validation and monitoring."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def validate_dataframe(
        self,
        df: pd.DataFrame,
        schema: Optional[DataSchema] = None,
        quality_thresholds: Optional[Dict[str, float]] = None
    ) -> DataQualityMetrics:
        """Validate DataFrame quality."""
        
        # Default quality thresholds
        thresholds = quality_thresholds or {
            'completeness': 0.95,
            'uniqueness': 0.8,
            'validity': 0.99,
            'consistency': 0.95,
            'timeliness': 0.9
        }
        
        metrics = self._calculate_quality_metrics(df)
        
        # Check against thresholds
        for metric, threshold in thresholds.items():
            if hasattr(metrics, metric):
                value = getattr(metrics, metric)
                if value < threshold:
                    raise DataQualityError(
                        f"Data quality check failed: {metric} = {value:.3f} < {threshold}",
                        details={
                            'metric': metric,
                            'value': value,
                            'threshold': threshold,
                            'dataframe_shape': df.shape
                        }
                    )
        
        return metrics
    
    def _calculate_quality_metrics(self, df: pd.DataFrame) -> DataQualityMetrics:
        """Calculate data quality metrics."""
        total_cells = df.size
        
        # Completeness: % of non-null values
        completeness = 1 - (df.isnull().sum().sum() / total_cells)
        
        # Uniqueness: average uniqueness across columns
        uniqueness_scores = []
        for col in df.columns:
            if len(df) > 0:
                unique_ratio = df[col].nunique() / len(df)
                uniqueness_scores.append(min(unique_ratio, 1.0))
        uniqueness = np.mean(uniqueness_scores) if uniqueness_scores else 0.0
        
        # Validity: % of values that match expected patterns/types
        validity = self._calculate_validity(df)
        
        # Consistency: % of values that are consistent across related columns
        consistency = self._calculate_consistency(df)
        
        # Timeliness: % of recent data (if timestamp columns exist)
        timeliness = self._calculate_timeliness(df)
        
        return DataQualityMetrics(
            completeness=completeness,
            uniqueness=uniqueness,
            validity=validity,
            consistency=consistency,
            timeliness=timeliness,
            accuracy=0.0  # Requires ground truth
        )
    
    def _calculate_validity(self, df: pd.DataFrame) -> float:
        """Calculate validity score."""
        valid_scores = []
        
        for col in df.columns:
            if df[col].dtype == 'object':
                # For string columns, check for reasonable length
                valid_count = df[col].str.len().between(1, 1000).sum()
            elif pd.api.types.is_numeric_dtype(df[col]):
                # For numeric columns, check for finite values
                valid_count = np.isfinite(df[col]).sum()
            else:
                # For other types, assume valid if not null
                valid_count = df[col].notna().sum()
            
            if len(df) > 0:
                valid_scores.append(valid_count / len(df))
        
        return np.mean(valid_scores) if valid_scores else 1.0
    
    def _calculate_consistency(self, df: pd.DataFrame) -> float:
        """Calculate consistency score."""
        # Simple consistency check: no contradictory values
        # This is a placeholder - implement domain-specific logic
        return 1.0
    
    def _calculate_timeliness(self, df: pd.DataFrame) -> float:
        """Calculate timeliness score."""
        timestamp_cols = []
        
        # Find timestamp columns
        for col in df.columns:
            if 'date' in col.lower() or 'time' in col.lower():
                try:
                    pd.to_datetime(df[col].dropna().head(100))
                    timestamp_cols.append(col)
                except:
                    continue
        
        if not timestamp_cols:
            return 1.0  # No timestamp data to evaluate
        
        # Check how much data is from the last 30 days
        cutoff_date = datetime.now() - timedelta(days=30)
        recent_scores = []
        
        for col in timestamp_cols:
            try:
                timestamps = pd.to_datetime(df[col].dropna())
                recent_count = (timestamps >= cutoff_date).sum()
                if len(timestamps) > 0:
                    recent_scores.append(recent_count / len(timestamps))
            except:
                continue
        
        return np.mean(recent_scores) if recent_scores else 1.0


class BigQueryDataSource(IDataSource):
    """Advanced BigQuery data source with caching and optimization."""
    
    def __init__(self, project_id: Optional[str] = None):
        """Initialize BigQuery data source."""
        self.settings = get_settings()
        self.project_id = project_id or self.settings.database.project_id
        self.client = None
        self.cache_manager = CacheManager()
        self.quality_validator = DataQualityValidator()
        self.logger = get_logger(__name__)
        
    def connect(self) -> None:
        """Connect to BigQuery."""
        try:
            self.client = bigquery.Client(project=self.project_id)
            self.logger.info("Connected to BigQuery", project_id=self.project_id)
        except Exception as e:
            raise DataError(f"Failed to connect to BigQuery: {e}")
    
    def disconnect(self) -> None:
        """Disconnect from BigQuery."""
        self.client = None
        self.logger.info("Disconnected from BigQuery")
    
    @log_performance("bigquery_query")
    def query(
        self,
        sql: str,
        use_cache: bool = True,
        cache_ttl: Optional[int] = None,
        validate_quality: bool = True,
        **kwargs
    ) -> pd.DataFrame:
        """Execute BigQuery query with caching and validation."""
        
        if not self.client:
            self.connect()
        
        # Check cache first
        if use_cache:
            cached_result = self.cache_manager.get(sql, kwargs)
            if cached_result is not None:
                return cached_result
        
        # Validate query
        if not self.validate_query(sql):
            raise DataError(f"Invalid SQL query: {sql[:100]}...")
        
        try:
            # Configure job
            job_config = bigquery.QueryJobConfig()
            job_config.maximum_bytes_billed = self.settings.database.max_bytes_billed
            
            # Execute query
            query_job = self.client.query(sql, job_config=job_config)
            df = query_job.to_dataframe()
            
            # Log query metadata
            metadata = QueryMetadata(
                query_hash=hashlib.md5(sql.encode()).hexdigest()[:8],
                execution_time=query_job.ended - query_job.started,
                rows_returned=len(df),
                bytes_processed=query_job.total_bytes_processed or 0,
                cache_hit=False,
                timestamp=datetime.utcnow()
            )
            
            self.logger.info(
                "Query executed",
                **metadata.dict()
            )
            
            # Validate data quality
            if validate_quality and len(df) > 0:
                try:
                    quality_metrics = self.quality_validator.validate_dataframe(df)
                    self.logger.info("Data quality validated", **quality_metrics.dict())
                except DataQualityError as e:
                    self.logger.warning("Data quality issues detected", error=str(e))
            
            # Cache result
            if use_cache and len(df) > 0:
                self.cache_manager.set(sql, df, kwargs, cache_ttl)
            
            return df
            
        except Exception as e:
            self.logger.error("Query execution failed", error=str(e), sql=sql[:200])
            raise DataError(f"Query execution failed: {e}")
    
    def get_schema(self, table_name: str) -> Dict[str, str]:
        """Get table schema."""
        if not self.client:
            self.connect()
        
        try:
            table_ref = self.client.get_table(table_name)
            schema = {}
            
            for field in table_ref.schema:
                schema[field.name] = field.field_type
            
            return schema
            
        except Exception as e:
            raise DataError(f"Failed to get schema for {table_name}: {e}")
    
    def validate_query(self, sql: str) -> bool:
        """Validate SQL query."""
        # Basic validation
        sql_lower = sql.lower().strip()
        
        # Check for dangerous operations
        dangerous_keywords = ['drop', 'delete', 'truncate', 'alter', 'create']
        for keyword in dangerous_keywords:
            if keyword in sql_lower:
                return False
        
        # Must be a SELECT statement
        if not sql_lower.startswith('select'):
            return False
        
        return True
