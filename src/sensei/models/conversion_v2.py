"""
Advanced Conversion Prediction Model for Sensei AI Suite v2.0.

Features:
- Ensemble of XGBoost + LightGBM + CatBoost
- Advanced feature engineering
- Automated hyperparameter optimization
- SHAP explainability
- Production-ready serving
"""

from typing import Any, Dict, List, Optional
import numpy as np
import pandas as pd
from sklearn.ensemble import VotingClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_selection import SelectKBest, mutual_info_classif
import xgboost as xgb
import lightgbm as lgb
import catboost as cb

from .base_v2 import BaseModelV2, ModelRegistry
from ..core.logging import get_logger
from ..core.exceptions import ModelNotTrainedError


@ModelRegistry.register("conversion_v2")
class ConversionModelV2(BaseModelV2):
    """
    Advanced conversion prediction model using ensemble methods.
    
    Combines XGBoost, LightGBM, and CatBoost for optimal performance.
    Includes automated feature engineering and selection.
    """
    
    model_name = "conversion_v2"
    version = "2.0.0"
    task_type = "classification"
    
    def __init__(self, **model_params):
        """Initialize conversion model."""
        
        # Default ensemble weights
        self.ensemble_weights = model_params.pop('ensemble_weights', [0.4, 0.35, 0.25])
        
        # Feature engineering parameters
        self.feature_selection = model_params.pop('feature_selection', True)
        self.max_features = model_params.pop('max_features', 50)
        self.create_interactions = model_params.pop('create_interactions', True)
        
        # Preprocessing components
        self.label_encoders = {}
        self.scaler = StandardScaler()
        self.feature_selector = None
        
        super().__init__(**model_params)
    
    def _create_model(self):
        """Create ensemble model with XGBoost, LightGBM, and CatBoost."""
        
        # XGBoost model
        xgb_params = {
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'max_depth': 6,
            'learning_rate': 0.1,
            'n_estimators': 100,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1,
            'random_state': 42,
            'n_jobs': -1
        }
        xgb_params.update(self.model_params.get('xgb_params', {}))
        xgb_model = xgb.XGBClassifier(**xgb_params)
        
        # LightGBM model
        lgb_params = {
            'objective': 'binary',
            'metric': 'auc',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.1,
            'n_estimators': 100,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1,
            'random_state': 42,
            'n_jobs': -1,
            'verbose': -1
        }
        lgb_params.update(self.model_params.get('lgb_params', {}))
        lgb_model = lgb.LGBMClassifier(**lgb_params)
        
        # CatBoost model
        cb_params = {
            'objective': 'Logloss',
            'eval_metric': 'AUC',
            'depth': 6,
            'learning_rate': 0.1,
            'iterations': 100,
            'l2_leaf_reg': 3,
            'random_seed': 42,
            'verbose': False,
            'thread_count': -1
        }
        cb_params.update(self.model_params.get('cb_params', {}))
        cb_model = cb.CatBoostClassifier(**cb_params)
        
        # Create ensemble
        estimators = [
            ('xgb', xgb_model),
            ('lgb', lgb_model),
            ('cb', cb_model)
        ]
        
        voting_method = self.model_params.get('voting', 'soft')
        ensemble = VotingClassifier(
            estimators=estimators,
            voting=voting_method,
            weights=self.ensemble_weights
        )
        
        return ensemble
    
    def _get_param_space(self) -> Dict[str, Any]:
        """Get hyperparameter search space for optimization."""
        return {
            'xgb_params__max_depth': {
                'type': 'int',
                'low': 3,
                'high': 10
            },
            'xgb_params__learning_rate': {
                'type': 'float',
                'low': 0.01,
                'high': 0.3,
                'log': True
            },
            'xgb_params__n_estimators': {
                'type': 'int',
                'low': 50,
                'high': 300
            },
            'xgb_params__subsample': {
                'type': 'float',
                'low': 0.6,
                'high': 1.0
            },
            'xgb_params__colsample_bytree': {
                'type': 'float',
                'low': 0.6,
                'high': 1.0
            },
            'lgb_params__num_leaves': {
                'type': 'int',
                'low': 10,
                'high': 100
            },
            'lgb_params__learning_rate': {
                'type': 'float',
                'low': 0.01,
                'high': 0.3,
                'log': True
            },
            'lgb_params__n_estimators': {
                'type': 'int',
                'low': 50,
                'high': 300
            },
            'cb_params__depth': {
                'type': 'int',
                'low': 4,
                'high': 10
            },
            'cb_params__learning_rate': {
                'type': 'float',
                'low': 0.01,
                'high': 0.3,
                'log': True
            },
            'cb_params__iterations': {
                'type': 'int',
                'low': 50,
                'high': 300
            }
        }
    
    def _prepare_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Advanced feature engineering and preparation."""
        
        X_processed = X.copy()
        
        # 1. Handle missing values
        X_processed = self._handle_missing_values(X_processed)
        
        # 2. Create interaction features
        if self.create_interactions:
            X_processed = self._create_interaction_features(X_processed)
        
        # 3. Encode categorical variables
        X_processed = self._encode_categorical_features(X_processed)
        
        # 4. Scale numerical features
        X_processed = self._scale_numerical_features(X_processed)
        
        # 5. Feature selection
        if self.feature_selection and self.is_fitted:
            X_processed = self._select_features(X_processed)
        
        return X_processed
    
    def _handle_missing_values(self, X: pd.DataFrame) -> pd.DataFrame:
        """Handle missing values with domain-specific logic."""
        
        X_filled = X.copy()
        
        # Numerical columns: fill with median
        numerical_cols = X_filled.select_dtypes(include=[np.number]).columns
        for col in numerical_cols:
            if X_filled[col].isnull().any():
                median_val = X_filled[col].median()
                X_filled[col] = X_filled[col].fillna(median_val)
        
        # Categorical columns: fill with mode or 'unknown'
        categorical_cols = X_filled.select_dtypes(include=['object', 'category']).columns
        for col in categorical_cols:
            if X_filled[col].isnull().any():
                mode_val = X_filled[col].mode()
                fill_val = mode_val[0] if len(mode_val) > 0 else 'unknown'
                X_filled[col] = X_filled[col].fillna(fill_val)
        
        return X_filled
    
    def _create_interaction_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Create interaction features for better model performance."""
        
        X_interactions = X.copy()
        
        # Identify numerical columns for interactions
        numerical_cols = X_interactions.select_dtypes(include=[np.number]).columns.tolist()
        
        # Create polynomial features for key numerical variables
        key_numerical = [col for col in numerical_cols if any(
            keyword in col.lower() for keyword in 
            ['score', 'budget', 'interaction', 'response', 'time']
        )]
        
        for col in key_numerical[:5]:  # Limit to avoid feature explosion
            if col in X_interactions.columns:
                # Squared terms
                X_interactions[f'{col}_squared'] = X_interactions[col] ** 2
                
                # Log transform (for positive values)
                if (X_interactions[col] > 0).all():
                    X_interactions[f'{col}_log'] = np.log1p(X_interactions[col])
        
        # Create ratio features
        if 'nb_interactions' in X_interactions.columns and 'nb_appels_historique' in X_interactions.columns:
            X_interactions['interaction_call_ratio'] = (
                X_interactions['nb_interactions'] / 
                (X_interactions['nb_appels_historique'] + 1)
            )
        
        # Create categorical interactions
        categorical_cols = X_interactions.select_dtypes(include=['object', 'category']).columns.tolist()
        if len(categorical_cols) >= 2:
            # Combine top 2 categorical features
            col1, col2 = categorical_cols[:2]
            X_interactions[f'{col1}_{col2}_interaction'] = (
                X_interactions[col1].astype(str) + '_' + X_interactions[col2].astype(str)
            )
        
        return X_interactions
    
    def _encode_categorical_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Encode categorical features with label encoding."""
        
        X_encoded = X.copy()
        categorical_cols = X_encoded.select_dtypes(include=['object', 'category']).columns
        
        for col in categorical_cols:
            if col not in self.label_encoders:
                # Fit new encoder
                self.label_encoders[col] = LabelEncoder()
                X_encoded[col] = self.label_encoders[col].fit_transform(X_encoded[col].astype(str))
            else:
                # Transform using existing encoder
                try:
                    X_encoded[col] = self.label_encoders[col].transform(X_encoded[col].astype(str))
                except ValueError:
                    # Handle unseen categories
                    known_categories = set(self.label_encoders[col].classes_)
                    X_encoded[col] = X_encoded[col].astype(str).apply(
                        lambda x: x if x in known_categories else 'unknown'
                    )
                    # Add 'unknown' to encoder if not present
                    if 'unknown' not in known_categories:
                        self.label_encoders[col].classes_ = np.append(
                            self.label_encoders[col].classes_, 'unknown'
                        )
                    X_encoded[col] = self.label_encoders[col].transform(X_encoded[col])
        
        return X_encoded
    
    def _scale_numerical_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Scale numerical features."""
        
        X_scaled = X.copy()
        numerical_cols = X_scaled.select_dtypes(include=[np.number]).columns
        
        if len(numerical_cols) > 0:
            if not hasattr(self.scaler, 'scale_'):
                # Fit scaler
                X_scaled[numerical_cols] = self.scaler.fit_transform(X_scaled[numerical_cols])
            else:
                # Transform using fitted scaler
                X_scaled[numerical_cols] = self.scaler.transform(X_scaled[numerical_cols])
        
        return X_scaled
    
    def _select_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Select most important features."""
        
        if self.feature_selector is None:
            return X
        
        try:
            X_selected = pd.DataFrame(
                self.feature_selector.transform(X),
                columns=X.columns[self.feature_selector.get_support()],
                index=X.index
            )
            return X_selected
        except Exception as e:
            self.logger.warning("Feature selection failed", error=str(e))
            return X
    
    def train(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        X_val: Optional[pd.DataFrame] = None,
        y_val: Optional[pd.Series] = None,
        **kwargs
    ):
        """Train the conversion model with feature selection."""
        
        # Prepare features
        X_prepared = self._prepare_features(X)
        
        # Feature selection during training
        if self.feature_selection and len(X_prepared.columns) > self.max_features:
            self.feature_selector = SelectKBest(
                score_func=mutual_info_classif,
                k=min(self.max_features, len(X_prepared.columns))
            )
            X_prepared = pd.DataFrame(
                self.feature_selector.fit_transform(X_prepared, y),
                columns=X_prepared.columns[self.feature_selector.get_support()],
                index=X_prepared.index
            )
        
        # Store prepared features for validation
        if X_val is not None:
            X_val_prepared = self._prepare_features(X_val)
            if self.feature_selector:
                X_val_prepared = pd.DataFrame(
                    self.feature_selector.transform(X_val_prepared),
                    columns=X_val_prepared.columns[self.feature_selector.get_support()],
                    index=X_val_prepared.index
                )
        else:
            X_val_prepared = None
        
        # Call parent train method
        return super().train(X_prepared, y, X_val_prepared, y_val, **kwargs)
    
    def get_ensemble_predictions(self, X: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Get predictions from individual ensemble members."""
        
        if not self.is_fitted:
            raise ModelNotTrainedError("Model must be trained before making predictions")
        
        X_prepared = self._prepare_features(X)
        
        predictions = {}
        for name, estimator in self.model.named_estimators_.items():
            predictions[name] = estimator.predict_proba(X_prepared)[:, 1]
        
        return predictions
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get aggregated feature importance from ensemble."""
        
        if not self.is_fitted:
            raise ModelNotTrainedError("Model must be trained to get feature importance")
        
        # Aggregate importance from all ensemble members
        importance_dict = {}
        
        for name, estimator in self.model.named_estimators_.items():
            if hasattr(estimator, 'feature_importances_'):
                importances = estimator.feature_importances_
                weight = self.ensemble_weights[list(self.model.named_estimators_.keys()).index(name)]
                
                for i, feature_name in enumerate(self.feature_names):
                    if feature_name not in importance_dict:
                        importance_dict[feature_name] = 0
                    importance_dict[feature_name] += importances[i] * weight
        
        return importance_dict
