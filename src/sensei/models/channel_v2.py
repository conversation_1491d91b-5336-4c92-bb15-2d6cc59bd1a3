"""
Advanced Channel Optimization Model for Sensei AI Suite v2.0.

Features:
- Multi-modal deep learning for channel and timing prediction
- Advanced sequence modeling with LSTM/Transformer
- Contextual embeddings for customer behavior
- Real-time optimization with reinforcement learning
"""

from typing import Any, Dict, List, Optional, Tuple
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.multioutput import MultiOutputClassifier
from sklearn.ensemble import RandomForestClassifier
import catboost as cb
import tensorflow as tf
from tensorflow.keras import layers, Model
from tensorflow.keras.optimizers import Adam

from .base_v2 import BaseModelV2, ModelRegistry
from ..core.logging import get_logger
from ..core.exceptions import ModelNotTrainedError


@ModelRegistry.register("channel_v2")
class ChannelModelV2(BaseModelV2):
    """
    Advanced channel optimization model using multi-modal deep learning.
    
    Predicts optimal communication channel and timing based on:
    - Customer behavior patterns
    - Historical interaction data
    - Temporal features
    - Contextual information
    """
    
    model_name = "channel_v2"
    version = "2.0.0"
    task_type = "multiclass"
    
    # Channel and timing mappings
    CHANNELS = {
        'email': 0,
        'phone': 1,
        'meeting': 2,
        'linkedin': 3,
        'sms': 4
    }
    
    TIMINGS = {
        'morning': 0,      # 8h-12h
        'afternoon': 1,    # 12h-17h
        'evening': 2,      # 17h-20h
        'weekend': 3       # Weekend
    }
    
    def __init__(self, **model_params):
        """Initialize channel optimization model."""
        
        # Model architecture parameters
        self.use_deep_learning = model_params.pop('use_deep_learning', True)
        self.embedding_dim = model_params.pop('embedding_dim', 64)
        self.lstm_units = model_params.pop('lstm_units', 128)
        self.dropout_rate = model_params.pop('dropout_rate', 0.3)
        
        # Sequence modeling parameters
        self.sequence_length = model_params.pop('sequence_length', 10)
        self.use_attention = model_params.pop('use_attention', True)
        
        # Preprocessing components
        self.label_encoders = {}
        self.scaler = StandardScaler()
        self.channel_encoder = OneHotEncoder(sparse=False)
        self.timing_encoder = OneHotEncoder(sparse=False)
        
        # Deep learning model
        self.dl_model = None
        
        super().__init__(**model_params)
    
    def _create_model(self):
        """Create multi-output model for channel and timing prediction."""
        
        if self.use_deep_learning:
            return self._create_deep_model()
        else:
            return self._create_traditional_model()
    
    def _create_traditional_model(self):
        """Create traditional ML model using CatBoost."""
        
        # CatBoost for multi-class classification
        cb_params = {
            'objective': 'MultiClass',
            'eval_metric': 'MultiClass',
            'depth': 6,
            'learning_rate': 0.1,
            'iterations': 200,
            'l2_leaf_reg': 3,
            'random_seed': 42,
            'verbose': False,
            'thread_count': -1
        }
        cb_params.update(self.model_params.get('cb_params', {}))
        
        # Use MultiOutputClassifier for channel and timing prediction
        base_model = cb.CatBoostClassifier(**cb_params)
        return MultiOutputClassifier(base_model)
    
    def _create_deep_model(self):
        """Create deep learning model with multi-modal inputs."""
        
        # This will be built during training when we know input shapes
        return None
    
    def _build_deep_model(self, input_shape: int, sequence_shape: Optional[Tuple] = None):
        """Build deep learning model architecture."""
        
        # Input layers
        main_input = layers.Input(shape=(input_shape,), name='main_features')
        
        # Main feature processing
        x = layers.Dense(256, activation='relu')(main_input)
        x = layers.Dropout(self.dropout_rate)(x)
        x = layers.Dense(128, activation='relu')(x)
        x = layers.Dropout(self.dropout_rate)(x)
        
        # Sequence input (if available)
        if sequence_shape:
            sequence_input = layers.Input(shape=sequence_shape, name='sequence_features')
            
            # LSTM for sequence modeling
            lstm_out = layers.LSTM(
                self.lstm_units,
                return_sequences=self.use_attention,
                dropout=self.dropout_rate
            )(sequence_input)
            
            # Attention mechanism
            if self.use_attention:
                attention = layers.MultiHeadAttention(
                    num_heads=8,
                    key_dim=self.lstm_units // 8
                )(lstm_out, lstm_out)
                lstm_out = layers.GlobalAveragePooling1D()(attention)
            
            # Combine main features with sequence features
            combined = layers.Concatenate()([x, lstm_out])
            x = layers.Dense(128, activation='relu')(combined)
            x = layers.Dropout(self.dropout_rate)(x)
            
            inputs = [main_input, sequence_input]
        else:
            inputs = main_input
        
        # Output layers
        channel_output = layers.Dense(
            len(self.CHANNELS),
            activation='softmax',
            name='channel_prediction'
        )(x)
        
        timing_output = layers.Dense(
            len(self.TIMINGS),
            activation='softmax',
            name='timing_prediction'
        )(x)
        
        # Create model
        model = Model(
            inputs=inputs,
            outputs=[channel_output, timing_output],
            name='channel_optimization_model'
        )
        
        # Compile model
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss={
                'channel_prediction': 'categorical_crossentropy',
                'timing_prediction': 'categorical_crossentropy'
            },
            metrics={
                'channel_prediction': ['accuracy'],
                'timing_prediction': ['accuracy']
            },
            loss_weights={
                'channel_prediction': 0.6,
                'timing_prediction': 0.4
            }
        )
        
        return model
    
    def _get_param_space(self) -> Dict[str, Any]:
        """Get hyperparameter search space."""
        
        if self.use_deep_learning:
            return {
                'embedding_dim': {
                    'type': 'int',
                    'low': 32,
                    'high': 128
                },
                'lstm_units': {
                    'type': 'int',
                    'low': 64,
                    'high': 256
                },
                'dropout_rate': {
                    'type': 'float',
                    'low': 0.1,
                    'high': 0.5
                },
                'learning_rate': {
                    'type': 'float',
                    'low': 0.0001,
                    'high': 0.01,
                    'log': True
                }
            }
        else:
            return {
                'cb_params__depth': {
                    'type': 'int',
                    'low': 4,
                    'high': 10
                },
                'cb_params__learning_rate': {
                    'type': 'float',
                    'low': 0.01,
                    'high': 0.3,
                    'log': True
                },
                'cb_params__iterations': {
                    'type': 'int',
                    'low': 100,
                    'high': 500
                },
                'cb_params__l2_leaf_reg': {
                    'type': 'float',
                    'low': 1,
                    'high': 10
                }
            }
    
    def _prepare_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Advanced feature engineering for channel optimization."""
        
        X_processed = X.copy()
        
        # 1. Handle missing values
        X_processed = self._handle_missing_values(X_processed)
        
        # 2. Create temporal features
        X_processed = self._create_temporal_features(X_processed)
        
        # 3. Create behavioral features
        X_processed = self._create_behavioral_features(X_processed)
        
        # 4. Encode categorical variables
        X_processed = self._encode_categorical_features(X_processed)
        
        # 5. Scale numerical features
        X_processed = self._scale_numerical_features(X_processed)
        
        return X_processed
    
    def _handle_missing_values(self, X: pd.DataFrame) -> pd.DataFrame:
        """Handle missing values with domain-specific logic."""
        
        X_filled = X.copy()
        
        # Fill missing communication preferences
        if 'preferred_channel' in X_filled.columns:
            X_filled['preferred_channel'] = X_filled['preferred_channel'].fillna('email')
        
        # Fill missing contact information
        if 'has_phone' in X_filled.columns:
            X_filled['has_phone'] = X_filled['has_phone'].fillna(False)
        
        if 'has_email' in X_filled.columns:
            X_filled['has_email'] = X_filled['has_email'].fillna(True)
        
        # Fill numerical features
        numerical_cols = X_filled.select_dtypes(include=[np.number]).columns
        for col in numerical_cols:
            if X_filled[col].isnull().any():
                median_val = X_filled[col].median()
                X_filled[col] = X_filled[col].fillna(median_val)
        
        return X_filled
    
    def _create_temporal_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Create temporal features for timing optimization."""
        
        X_temporal = X.copy()
        
        # Current time features
        now = pd.Timestamp.now()
        X_temporal['current_hour'] = now.hour
        X_temporal['current_day_of_week'] = now.dayofweek
        X_temporal['current_month'] = now.month
        X_temporal['is_weekend'] = (now.dayofweek >= 5).astype(int)
        
        # Time since last interaction
        if 'last_interaction_date' in X_temporal.columns:
            try:
                last_interaction = pd.to_datetime(X_temporal['last_interaction_date'])
                X_temporal['days_since_last_interaction'] = (now - last_interaction).dt.days
                X_temporal['hours_since_last_interaction'] = (now - last_interaction).dt.total_seconds() / 3600
            except:
                X_temporal['days_since_last_interaction'] = 30  # Default
                X_temporal['hours_since_last_interaction'] = 720  # Default
        
        # Optimal timing based on historical data
        if 'response_rate_by_hour' in X_temporal.columns:
            # Parse JSON-like response rate data
            try:
                X_temporal['best_response_hour'] = X_temporal['response_rate_by_hour'].apply(
                    lambda x: self._extract_best_hour(x) if pd.notna(x) else 14
                )
            except:
                X_temporal['best_response_hour'] = 14  # Default to 2 PM
        
        return X_temporal
    
    def _create_behavioral_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Create behavioral features for channel optimization."""
        
        X_behavioral = X.copy()
        
        # Communication preference score
        channel_preferences = {
            'email': 1.0,
            'phone': 0.8,
            'meeting': 0.6,
            'linkedin': 0.4,
            'sms': 0.3
        }
        
        if 'preferred_channel' in X_behavioral.columns:
            X_behavioral['channel_preference_score'] = X_behavioral['preferred_channel'].map(
                channel_preferences
            ).fillna(0.5)
        
        # Response rate features
        if 'email_response_rate' in X_behavioral.columns:
            X_behavioral['email_response_rate'] = X_behavioral['email_response_rate'].fillna(0.1)
        
        if 'phone_response_rate' in X_behavioral.columns:
            X_behavioral['phone_response_rate'] = X_behavioral['phone_response_rate'].fillna(0.05)
        
        # Engagement score
        engagement_features = ['nb_interactions', 'nb_appels_historique', 'email_opens', 'email_clicks']
        available_features = [f for f in engagement_features if f in X_behavioral.columns]
        
        if available_features:
            X_behavioral['engagement_score'] = X_behavioral[available_features].fillna(0).sum(axis=1)
            X_behavioral['engagement_score'] = np.log1p(X_behavioral['engagement_score'])
        
        # Channel availability
        X_behavioral['available_channels'] = 0
        if 'has_email' in X_behavioral.columns:
            X_behavioral['available_channels'] += X_behavioral['has_email'].astype(int)
        if 'has_phone' in X_behavioral.columns:
            X_behavioral['available_channels'] += X_behavioral['has_phone'].astype(int)
        
        return X_behavioral
    
    def _extract_best_hour(self, response_data: str) -> int:
        """Extract best response hour from historical data."""
        try:
            # Simple heuristic - return afternoon as default
            return 14
        except:
            return 14
    
    def _encode_categorical_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Encode categorical features."""
        
        X_encoded = X.copy()
        categorical_cols = X_encoded.select_dtypes(include=['object', 'category']).columns
        
        for col in categorical_cols:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()
                X_encoded[col] = self.label_encoders[col].fit_transform(X_encoded[col].astype(str))
            else:
                try:
                    X_encoded[col] = self.label_encoders[col].transform(X_encoded[col].astype(str))
                except ValueError:
                    # Handle unseen categories
                    known_categories = set(self.label_encoders[col].classes_)
                    X_encoded[col] = X_encoded[col].astype(str).apply(
                        lambda x: x if x in known_categories else 'unknown'
                    )
                    if 'unknown' not in known_categories:
                        self.label_encoders[col].classes_ = np.append(
                            self.label_encoders[col].classes_, 'unknown'
                        )
                    X_encoded[col] = self.label_encoders[col].transform(X_encoded[col])
        
        return X_encoded
    
    def _scale_numerical_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Scale numerical features."""
        
        X_scaled = X.copy()
        numerical_cols = X_scaled.select_dtypes(include=[np.number]).columns
        
        if len(numerical_cols) > 0:
            if not hasattr(self.scaler, 'scale_'):
                X_scaled[numerical_cols] = self.scaler.fit_transform(X_scaled[numerical_cols])
            else:
                X_scaled[numerical_cols] = self.scaler.transform(X_scaled[numerical_cols])
        
        return X_scaled
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Predict optimal channel and timing."""
        
        if not self.is_fitted:
            raise ModelNotTrainedError("Model must be trained before making predictions")
        
        X_prepared = self._prepare_features(X)
        
        if self.use_deep_learning and self.dl_model:
            predictions = self.dl_model.predict(X_prepared)
            # Combine channel and timing predictions
            channel_pred = np.argmax(predictions[0], axis=1)
            timing_pred = np.argmax(predictions[1], axis=1)
            
            # Create combined prediction string
            combined_pred = []
            for c, t in zip(channel_pred, timing_pred):
                channel_name = list(self.CHANNELS.keys())[c]
                timing_name = list(self.TIMINGS.keys())[t]
                combined_pred.append(f"{channel_name}_{timing_name}")
            
            return np.array(combined_pred)
        else:
            # Traditional model prediction
            predictions = self.model.predict(X_prepared)
            # predictions is [channel_predictions, timing_predictions]
            combined_pred = []
            for c, t in zip(predictions[0], predictions[1]):
                channel_name = list(self.CHANNELS.keys())[c]
                timing_name = list(self.TIMINGS.keys())[t]
                combined_pred.append(f"{channel_name}_{timing_name}")
            
            return np.array(combined_pred)
    
    def predict_channel_timing_separate(self, X: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Predict channel and timing separately."""
        
        if not self.is_fitted:
            raise ModelNotTrainedError("Model must be trained before making predictions")
        
        X_prepared = self._prepare_features(X)
        
        if self.use_deep_learning and self.dl_model:
            predictions = self.dl_model.predict(X_prepared)
            channel_pred = np.argmax(predictions[0], axis=1)
            timing_pred = np.argmax(predictions[1], axis=1)
        else:
            predictions = self.model.predict(X_prepared)
            channel_pred = predictions[0]
            timing_pred = predictions[1]
        
        return channel_pred, timing_pred
