"""
Sensei AI Suite v2.0 - ML Models Package

Advanced ML models with state-of-the-art algorithms:
- ConversionModelV2: Ensemble prediction for conversion probability
- ChannelModelV2: Multi-modal optimization for communication channels
- NLPModelV2: Transformer-based NLP analysis
"""

# Import v2.0 models for automatic registration
from .base_v2 import BaseModelV2, ModelRegistry
from .conversion_v2 import ConversionModelV2
from .channel_v2 import ChannelModelV2
from .nlp_v2 import NLPModelV2

__all__ = [
    "BaseModelV2",
    "ModelRegistry",
    "ConversionModelV2",
    "ChannelModelV2",
    "NLPModelV2"
]
