"""
Advanced base model class for Sensei AI Suite v2.0.

Features:
- Modern ML algorithms with ensemble support
- Automated hyperparameter optimization
- Advanced validation and overfitting detection
- Model explainability and interpretability
- Production-ready model serving
"""

import abc
import json
import pickle
import joblib
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime
from pathlib import Path

import pandas as pd
import numpy as np
from sklearn.model_selection import cross_val_score, StratifiedKFold, TimeSeriesSplit
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, average_precision_score, log_loss,
    mean_squared_error, mean_absolute_error, r2_score
)
import optuna
import shap
from lime import lime_tabular

from ..core.config import get_settings
from ..core.exceptions import ModelError, ModelNotTrainedError, HyperparameterError
from ..core.interfaces import IModel, ModelMetrics, ModelInfo, PredictionResult
from ..core.logging import get_logger, log_performance


class ModelRegistry:
    """Global model registry for v2.0."""
    _models: Dict[str, type] = {}
    
    @classmethod
    def register(cls, name: str):
        """Decorator to register a model."""
        def decorator(model_class):
            cls._models[name] = model_class
            model_class.model_name = name
            return model_class
        return decorator
    
    @classmethod
    def get_model_class(cls, name: str) -> type:
        """Get model class by name."""
        if name not in cls._models:
            raise ModelError(f"Model '{name}' not found in registry")
        return cls._models[name]
    
    @classmethod
    def list_models(cls) -> List[str]:
        """List all registered models."""
        return list(cls._models.keys())


class HyperparameterOptimizer:
    """Advanced hyperparameter optimization with Optuna."""
    
    def __init__(self, model: 'BaseModelV2'):
        self.model = model
        self.study = None
        self.logger = get_logger(__name__)
    
    def optimize(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        param_space: Dict[str, Any],
        n_trials: int = 100,
        cv_folds: int = 5,
        scoring: str = 'roc_auc',
        timeout: Optional[int] = None
    ) -> Dict[str, Any]:
        """Optimize hyperparameters using Optuna."""
        
        def objective(trial):
            # Sample parameters
            params = {}
            for param_name, param_config in param_space.items():
                if param_config['type'] == 'categorical':
                    params[param_name] = trial.suggest_categorical(
                        param_name, param_config['choices']
                    )
                elif param_config['type'] == 'int':
                    params[param_name] = trial.suggest_int(
                        param_name, param_config['low'], param_config['high']
                    )
                elif param_config['type'] == 'float':
                    params[param_name] = trial.suggest_float(
                        param_name, param_config['low'], param_config['high'],
                        log=param_config.get('log', False)
                    )
            
            # Create model with suggested parameters
            model_instance = self.model.__class__(**params)
            
            # Cross-validation
            if self.model.task_type == 'classification':
                cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
            else:
                cv = TimeSeriesSplit(n_splits=cv_folds)
            
            scores = cross_val_score(
                model_instance._create_model(),
                X, y, cv=cv, scoring=scoring, n_jobs=-1
            )
            
            return scores.mean()
        
        # Create study
        study_name = f"{self.model.model_name}_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.study = optuna.create_study(
            direction='maximize',
            study_name=study_name,
            sampler=optuna.samplers.TPESampler(seed=42)
        )
        
        # Optimize
        self.study.optimize(
            objective,
            n_trials=n_trials,
            timeout=timeout,
            show_progress_bar=True
        )
        
        best_params = self.study.best_params
        best_score = self.study.best_value
        
        self.logger.info(
            "Hyperparameter optimization completed",
            model_name=self.model.model_name,
            best_score=best_score,
            best_params=best_params,
            n_trials=len(self.study.trials)
        )
        
        return best_params


class ModelExplainer:
    """Model explainability and interpretability."""
    
    def __init__(self, model: 'BaseModelV2'):
        self.model = model
        self.shap_explainer = None
        self.lime_explainer = None
        self.logger = get_logger(__name__)
    
    def setup_explainers(self, X_train: pd.DataFrame):
        """Setup SHAP and LIME explainers."""
        try:
            # SHAP explainer
            if hasattr(self.model.model, 'predict_proba'):
                self.shap_explainer = shap.Explainer(
                    self.model.model.predict_proba,
                    X_train.sample(min(100, len(X_train)))
                )
            else:
                self.shap_explainer = shap.Explainer(
                    self.model.model.predict,
                    X_train.sample(min(100, len(X_train)))
                )
            
            # LIME explainer
            self.lime_explainer = lime_tabular.LimeTabularExplainer(
                X_train.values,
                feature_names=X_train.columns.tolist(),
                class_names=['0', '1'] if self.model.task_type == 'classification' else None,
                mode='classification' if self.model.task_type == 'classification' else 'regression'
            )
            
        except Exception as e:
            self.logger.warning("Failed to setup explainers", error=str(e))
    
    def explain_prediction(
        self,
        X: pd.DataFrame,
        method: str = 'shap'
    ) -> Dict[str, Any]:
        """Explain individual predictions."""
        
        if method == 'shap' and self.shap_explainer:
            try:
                shap_values = self.shap_explainer(X)
                return {
                    'method': 'shap',
                    'feature_importance': dict(zip(X.columns, shap_values.values[0])),
                    'base_value': shap_values.base_values[0] if hasattr(shap_values, 'base_values') else 0
                }
            except Exception as e:
                self.logger.warning("SHAP explanation failed", error=str(e))
        
        elif method == 'lime' and self.lime_explainer:
            try:
                explanation = self.lime_explainer.explain_instance(
                    X.iloc[0].values,
                    self.model.model.predict_proba if hasattr(self.model.model, 'predict_proba') else self.model.model.predict,
                    num_features=min(10, len(X.columns))
                )
                
                feature_importance = dict(explanation.as_list())
                return {
                    'method': 'lime',
                    'feature_importance': feature_importance,
                    'score': explanation.score
                }
            except Exception as e:
                self.logger.warning("LIME explanation failed", error=str(e))
        
        return {'method': 'none', 'feature_importance': {}}


class BaseModelV2(IModel):
    """
    Advanced base model class for Sensei AI Suite v2.0.
    
    Provides a unified interface for all ML models with:
    - Automated hyperparameter optimization
    - Advanced validation and metrics
    - Model explainability
    - Production-ready serving
    """
    
    model_name: str = "base_v2"
    version: str = "2.0.0"
    task_type: str = "classification"  # or "regression"
    
    def __init__(self, **model_params):
        """Initialize the model."""
        self.model_params = model_params
        self.model = None
        self.feature_names: Optional[List[str]] = None
        self.target_name: Optional[str] = None
        self.is_fitted = False
        self.training_metadata: Dict[str, Any] = {}
        self.metrics: Optional[ModelMetrics] = None
        
        # Advanced components
        self.optimizer = HyperparameterOptimizer(self)
        self.explainer = ModelExplainer(self)
        
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        
        # Create the underlying model
        self.model = self._create_model()
    
    @property
    def name(self) -> str:
        """Model name."""
        return self.model_name
    
    @property
    def is_trained(self) -> bool:
        """Check if model is trained."""
        return self.is_fitted
    
    @abc.abstractmethod
    def _create_model(self):
        """Create the underlying ML model."""
        pass
    
    @abc.abstractmethod
    def _get_param_space(self) -> Dict[str, Any]:
        """Get hyperparameter search space."""
        pass
    
    def _prepare_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for training/prediction."""
        # Basic feature preparation - override in subclasses
        return X.copy()
    
    def _calculate_metrics(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray,
        y_proba: Optional[np.ndarray] = None,
        training_time: Optional[float] = None
    ) -> ModelMetrics:
        """Calculate comprehensive model metrics."""
        
        metrics_dict = {
            'training_time': training_time,
            'feature_count': len(self.feature_names) if self.feature_names else 0,
            'sample_count': len(y_true)
        }
        
        if self.task_type == 'classification':
            metrics_dict.update({
                'accuracy': accuracy_score(y_true, y_pred),
                'precision': precision_score(y_true, y_pred, average='weighted', zero_division=0),
                'recall': recall_score(y_true, y_pred, average='weighted', zero_division=0),
                'f1_score': f1_score(y_true, y_pred, average='weighted', zero_division=0)
            })
            
            if y_proba is not None:
                try:
                    if y_proba.ndim > 1 and y_proba.shape[1] > 1:
                        # Multi-class
                        metrics_dict['auc_roc'] = roc_auc_score(y_true, y_proba, multi_class='ovr', average='weighted')
                    else:
                        # Binary
                        y_proba_binary = y_proba[:, 1] if y_proba.ndim > 1 else y_proba
                        metrics_dict['auc_roc'] = roc_auc_score(y_true, y_proba_binary)
                        metrics_dict['auc_pr'] = average_precision_score(y_true, y_proba_binary)
                        metrics_dict['log_loss'] = log_loss(y_true, y_proba_binary)
                except Exception as e:
                    self.logger.warning("Failed to calculate probabilistic metrics", error=str(e))
        
        else:  # regression
            metrics_dict.update({
                'mse': mean_squared_error(y_true, y_pred),
                'mae': mean_absolute_error(y_true, y_pred),
                'r2_score': r2_score(y_true, y_pred)
            })
        
        return ModelMetrics(**metrics_dict)
    
    @log_performance("model_training")
    def train(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        X_val: Optional[pd.DataFrame] = None,
        y_val: Optional[pd.Series] = None,
        optimize_hyperparams: bool = False,
        **kwargs
    ) -> ModelMetrics:
        """Train the model with advanced features."""
        
        start_time = datetime.now()
        
        # Store feature and target names
        self.feature_names = X.columns.tolist()
        self.target_name = y.name or 'target'
        
        # Prepare features
        X_prepared = self._prepare_features(X)
        X_val_prepared = self._prepare_features(X_val) if X_val is not None else None
        
        # Hyperparameter optimization
        if optimize_hyperparams:
            try:
                param_space = self._get_param_space()
                best_params = self.optimizer.optimize(
                    X_prepared, y, param_space,
                    n_trials=self.settings.ml.hyperopt_trials
                )
                
                # Update model with best parameters
                self.model_params.update(best_params)
                self.model = self._create_model()
                
            except Exception as e:
                self.logger.warning("Hyperparameter optimization failed", error=str(e))
        
        # Train the model
        try:
            if X_val_prepared is not None and y_val is not None:
                # Use validation set if provided
                self.model.fit(
                    X_prepared, y,
                    eval_set=[(X_val_prepared, y_val)],
                    **kwargs
                )
            else:
                self.model.fit(X_prepared, y, **kwargs)
            
            self.is_fitted = True
            
        except Exception as e:
            raise ModelError(f"Model training failed: {e}")
        
        # Calculate training time
        training_time = (datetime.now() - start_time).total_seconds()
        
        # Calculate metrics
        y_pred = self.predict(X)
        y_proba = self.predict_proba(X) if hasattr(self, 'predict_proba') else None
        
        self.metrics = self._calculate_metrics(y.values, y_pred, y_proba, training_time)
        
        # Setup explainers
        self.explainer.setup_explainers(X_prepared)
        
        # Store training metadata
        self.training_metadata = {
            'trained_at': datetime.now().isoformat(),
            'training_samples': len(X),
            'validation_samples': len(X_val) if X_val is not None else 0,
            'feature_count': len(self.feature_names),
            'hyperparameter_optimization': optimize_hyperparams,
            'model_params': self.model_params
        }
        
        self.logger.info(
            "Model training completed",
            model_name=self.model_name,
            training_time=training_time,
            **self.metrics.dict()
        )
        
        return self.metrics
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions."""
        if not self.is_fitted:
            raise ModelNotTrainedError("Model must be trained before making predictions")
        
        X_prepared = self._prepare_features(X)
        return self.model.predict(X_prepared)
    
    def predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Predict probabilities (for classification models)."""
        if not self.is_fitted:
            raise ModelNotTrainedError("Model must be trained before making predictions")
        
        if not hasattr(self.model, 'predict_proba'):
            raise ModelError("Model does not support probability predictions")
        
        X_prepared = self._prepare_features(X)
        return self.model.predict_proba(X_prepared)
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance scores."""
        if not self.is_fitted:
            raise ModelNotTrainedError("Model must be trained to get feature importance")
        
        if hasattr(self.model, 'feature_importances_'):
            importance_scores = self.model.feature_importances_
        elif hasattr(self.model, 'coef_'):
            importance_scores = np.abs(self.model.coef_).flatten()
        else:
            return {}
        
        if self.feature_names and len(importance_scores) == len(self.feature_names):
            return dict(zip(self.feature_names, importance_scores))
        else:
            return {}
    
    def save(self, path: Path) -> None:
        """Save model to disk."""
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        
        # Save model
        model_path = path / "model.joblib"
        joblib.dump(self.model, model_path)
        
        # Save metadata
        metadata = {
            'model_name': self.model_name,
            'version': self.version,
            'task_type': self.task_type,
            'model_params': self.model_params,
            'feature_names': self.feature_names,
            'target_name': self.target_name,
            'is_fitted': self.is_fitted,
            'training_metadata': self.training_metadata,
            'metrics': self.metrics.dict() if self.metrics else None
        }
        
        metadata_path = path / "metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        self.logger.info("Model saved", path=str(path))
    
    def load(self, path: Path) -> None:
        """Load model from disk."""
        path = Path(path)
        
        # Load model
        model_path = path / "model.joblib"
        if not model_path.exists():
            raise ModelError(f"Model file not found: {model_path}")
        
        self.model = joblib.load(model_path)
        
        # Load metadata
        metadata_path = path / "metadata.json"
        if metadata_path.exists():
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            
            self.model_name = metadata.get('model_name', self.model_name)
            self.version = metadata.get('version', self.version)
            self.task_type = metadata.get('task_type', self.task_type)
            self.model_params = metadata.get('model_params', {})
            self.feature_names = metadata.get('feature_names')
            self.target_name = metadata.get('target_name')
            self.is_fitted = metadata.get('is_fitted', False)
            self.training_metadata = metadata.get('training_metadata', {})
            
            metrics_data = metadata.get('metrics')
            if metrics_data:
                self.metrics = ModelMetrics(**metrics_data)
        
        self.logger.info("Model loaded", path=str(path))
    
    def get_info(self) -> ModelInfo:
        """Get model information."""
        return ModelInfo(
            name=self.model_name,
            version=self.version,
            algorithm=self.__class__.__name__,
            created_at=datetime.fromisoformat(self.training_metadata.get('trained_at', datetime.now().isoformat())),
            updated_at=datetime.now(),
            metrics=self.metrics or ModelMetrics(),
            hyperparameters=self.model_params,
            feature_names=self.feature_names or [],
            target_name=self.target_name or 'target',
            description=f"{self.model_name} model v{self.version}"
        )
