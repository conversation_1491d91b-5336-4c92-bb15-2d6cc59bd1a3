"""
Main FastAPI application for Sensei AI Suite v2.0.

Features:
- Modern FastAPI with async/await
- Comprehensive error handling
- Rate limiting and security
- Real-time monitoring
- Auto-generated documentation
- Health checks and metrics
"""

import time
from contextlib import asynccontextmanager
from typing import Dict, List

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
import uvicorn

from ...core.config import get_settings
from ...core.exceptions import SenseiException, ModelError, ValidationError
from ...core.logging import get_logger, log_performance
from .services import PredictionService
from .models import (
    ConversionRequest, ConversionResponse,
    ChannelRequest, ChannelResponse,
    NLPRequest, NLPResponse,
    HealthResponse, ModelInfoResponse,
    ErrorResponse, BatchRequest, BatchResponse
)


# Global instances
prediction_service: PredictionService = None
logger = get_logger(__name__)
security = HTTPBearer(auto_error=False)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management."""
    global prediction_service
    
    settings = get_settings()
    logger.info("Starting Sensei AI Suite v2.0", version=settings.version)
    
    # Initialize services
    prediction_service = PredictionService()
    await prediction_service.initialize()
    
    logger.info("Sensei AI Suite v2.0 ready")
    yield
    
    logger.info("Shutting down Sensei AI Suite v2.0")


# Create FastAPI app
settings = get_settings()
app = FastAPI(
    title="Sensei AI Suite v2.0",
    description="Next-generation ML platform for B2B sales intelligence",
    version=settings.version,
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    openapi_url="/openapi.json" if settings.debug else None
)

# Middleware
app.add_middleware(GZipMiddleware, minimum_size=1000)

if settings.api.cors_origins:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.api.cors_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE"],
        allow_headers=["*"],
    )

if settings.is_production():
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "*.sensei-ai.com"]
    )


# Dependency injection
async def get_prediction_service() -> PredictionService:
    """Get prediction service instance."""
    if prediction_service is None:
        raise HTTPException(status_code=503, detail="Service not initialized")
    return prediction_service


async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify API token (simplified implementation)."""
    if settings.is_production() and not credentials:
        raise HTTPException(status_code=401, detail="Authentication required")
    return credentials


# Middleware for request timing
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Add processing time to response headers."""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# Exception handlers
@app.exception_handler(SenseiException)
async def sensei_exception_handler(request: Request, exc: SenseiException):
    """Handle Sensei-specific exceptions."""
    logger.error("Sensei exception", error=exc.to_dict())
    return JSONResponse(
        status_code=400,
        content=ErrorResponse(
            error_code=exc.error_code,
            error_message=exc.message,
            error_details=exc.details
        ).dict()
    )


@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    """Handle validation errors."""
    return JSONResponse(
        status_code=422,
        content=ErrorResponse(
            error_code="VALIDATION_ERROR",
            error_message="Request validation failed",
            error_details={"validation_errors": str(exc)}
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.error("Unhandled exception", error=str(exc), path=request.url.path)
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error_code="INTERNAL_ERROR",
            error_message="Internal server error",
            error_details={"error": str(exc) if settings.debug else "Contact support"}
        ).dict()
    )


# Health and status endpoints
@app.get("/health", response_model=HealthResponse, tags=["Health"])
async def health_check(service: PredictionService = Depends(get_prediction_service)):
    """Health check endpoint."""
    import psutil
    
    model_status = service.get_model_status()
    uptime = service.get_uptime()
    
    # System metrics
    system_metrics = {
        "cpu_percent": psutil.cpu_percent(),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_percent": psutil.disk_usage('/').percent
    }
    
    # Overall status
    status = "healthy"
    if any(status.value == "error" for status in model_status.values()):
        status = "degraded"
    
    return HealthResponse(
        status=status,
        version=settings.version,
        uptime_seconds=uptime,
        models_status=model_status,
        system_metrics=system_metrics
    )


@app.get("/models", response_model=List[ModelInfoResponse], tags=["Models"])
async def list_models(
    service: PredictionService = Depends(get_prediction_service),
    _: HTTPAuthorizationCredentials = Depends(verify_token)
):
    """List all available models."""
    try:
        models = service.list_models()
        return [
            ModelInfoResponse(
                model_name=model.name,
                model_version=model.version,
                model_type=model.algorithm,
                status="ready",  # Simplified
                last_updated=model.updated_at,
                metrics=model.metrics.dict() if model.metrics else {},
                feature_count=len(model.feature_names),
                training_samples=model.metrics.sample_count if model.metrics else 0
            )
            for model in models
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list models: {e}")


@app.get("/models/{model_name}", response_model=ModelInfoResponse, tags=["Models"])
async def get_model_info(
    model_name: str,
    service: PredictionService = Depends(get_prediction_service),
    _: HTTPAuthorizationCredentials = Depends(verify_token)
):
    """Get information about a specific model."""
    try:
        model = service.get_model_info(model_name)
        return ModelInfoResponse(
            model_name=model.name,
            model_version=model.version,
            model_type=model.algorithm,
            status="ready",  # Simplified
            last_updated=model.updated_at,
            metrics=model.metrics.dict() if model.metrics else {},
            feature_count=len(model.feature_names),
            training_samples=model.metrics.sample_count if model.metrics else 0
        )
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Model not found: {e}")


# Prediction endpoints
@app.post("/predict/conversion", response_model=ConversionResponse, tags=["Predictions"])
@log_performance("api_conversion_prediction")
async def predict_conversion(
    request: ConversionRequest,
    background_tasks: BackgroundTasks,
    service: PredictionService = Depends(get_prediction_service),
    _: HTTPAuthorizationCredentials = Depends(verify_token)
):
    """Predict conversion probability for a prospect."""
    try:
        result = await service.predict_conversion(request)
        
        # Log prediction for analytics (background task)
        background_tasks.add_task(
            log_prediction,
            "conversion",
            request.prospect_id,
            result.conversion_probability
        )
        
        return result
    except Exception as e:
        logger.error("Conversion prediction failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/predict/channel", response_model=ChannelResponse, tags=["Predictions"])
@log_performance("api_channel_optimization")
async def optimize_channel(
    request: ChannelRequest,
    background_tasks: BackgroundTasks,
    service: PredictionService = Depends(get_prediction_service),
    _: HTTPAuthorizationCredentials = Depends(verify_token)
):
    """Optimize communication channel and timing for a prospect."""
    try:
        result = await service.optimize_channel(request)
        
        # Log prediction for analytics
        background_tasks.add_task(
            log_prediction,
            "channel",
            request.prospect_id,
            f"{result.optimal_channel}_{result.optimal_timing}"
        )
        
        return result
    except Exception as e:
        logger.error("Channel optimization failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/predict/nlp", response_model=NLPResponse, tags=["Predictions"])
@log_performance("api_nlp_analysis")
async def analyze_text(
    request: NLPRequest,
    service: PredictionService = Depends(get_prediction_service),
    _: HTTPAuthorizationCredentials = Depends(verify_token)
):
    """Perform comprehensive NLP analysis on text content."""
    try:
        result = await service.analyze_text(request)
        return result
    except Exception as e:
        logger.error("NLP analysis failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# Batch processing endpoints
@app.post("/batch/conversion", response_model=BatchResponse, tags=["Batch"])
async def batch_predict_conversion(
    request: BatchRequest,
    background_tasks: BackgroundTasks,
    service: PredictionService = Depends(get_prediction_service),
    _: HTTPAuthorizationCredentials = Depends(verify_token)
):
    """Batch conversion prediction."""
    try:
        results = []
        errors = []
        
        for i, item in enumerate(request.items):
            try:
                conv_request = ConversionRequest(**item)
                result = await service.predict_conversion(conv_request)
                results.append(result.dict())
            except Exception as e:
                errors.append({
                    "index": i,
                    "error": str(e),
                    "item": item
                })
        
        return BatchResponse(
            batch_id=request.batch_id or f"batch_{int(time.time())}",
            total_items=len(request.items),
            successful_items=len(results),
            failed_items=len(errors),
            results=results,
            errors=errors
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch processing failed: {e}")


# Background tasks
async def log_prediction(model_type: str, entity_id: str, prediction: any):
    """Log prediction for analytics (background task)."""
    logger.info(
        "Prediction logged",
        model_type=model_type,
        entity_id=entity_id,
        prediction=str(prediction)
    )


# Metrics endpoint for monitoring
@app.get("/metrics", tags=["Monitoring"])
async def get_metrics():
    """Get application metrics in Prometheus format."""
    # This would return Prometheus-formatted metrics
    return {"message": "Metrics endpoint - implement Prometheus integration"}


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.reload,
        workers=settings.api.workers if not settings.api.reload else 1,
        log_level=settings.api.log_level.lower()
    )
