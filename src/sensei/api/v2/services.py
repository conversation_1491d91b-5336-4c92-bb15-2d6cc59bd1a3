"""
Business logic services for Sensei AI Suite v2.0 API.

Implements the service layer pattern with:
- Model management and caching
- Prediction orchestration
- Error handling and recovery
- Performance monitoring
"""

import asyncio
import time
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
from pathlib import Path

import pandas as pd
import numpy as np
from fastapi import HTT<PERSON>Ex<PERSON>

from ...core.config import get_settings
from ...core.exceptions import ModelError, ModelNotFoundError, ModelNotTrainedError
from ...core.interfaces import IPredictionService, ModelInfo, PredictionResult
from ...core.logging import get_logger, log_performance
from ...models.base_v2 import ModelRegistry
from ...models.conversion_v2 import ConversionModelV2
from ...models.channel_v2 import ChannelModelV2
from ...models.nlp_v2 import NLPModelV2
from .models import (
    ConversionRequest, ConversionResponse,
    ChannelRequest, ChannelResponse,
    NLPRequest, NLPResponse,
    ModelStatus, SentimentResult, IntentResult, TopicResult,
    ChannelType, TimingType
)


class ModelCache:
    """Thread-safe model cache with automatic cleanup."""
    
    def __init__(self, max_size: int = 10, ttl_seconds: int = 3600):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._access_times: Dict[str, float] = {}
        self.logger = get_logger(__name__)
    
    def get(self, model_name: str, version: Optional[str] = None) -> Optional[Any]:
        """Get model from cache."""
        cache_key = f"{model_name}:{version or 'latest'}"
        
        if cache_key in self._cache:
            # Check TTL
            if time.time() - self._access_times[cache_key] < self.ttl_seconds:
                self._access_times[cache_key] = time.time()
                return self._cache[cache_key]['model']
            else:
                # Expired
                self._remove(cache_key)
        
        return None
    
    def put(self, model_name: str, model: Any, version: Optional[str] = None) -> None:
        """Put model in cache."""
        cache_key = f"{model_name}:{version or 'latest'}"
        
        # Cleanup if needed
        if len(self._cache) >= self.max_size:
            self._cleanup_oldest()
        
        self._cache[cache_key] = {
            'model': model,
            'cached_at': datetime.utcnow(),
            'version': version
        }
        self._access_times[cache_key] = time.time()
        
        self.logger.debug("Model cached", model_name=model_name, version=version)
    
    def _remove(self, cache_key: str) -> None:
        """Remove model from cache."""
        if cache_key in self._cache:
            del self._cache[cache_key]
            del self._access_times[cache_key]
    
    def _cleanup_oldest(self) -> None:
        """Remove oldest accessed model."""
        if self._access_times:
            oldest_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
            self._remove(oldest_key)
    
    def clear(self) -> None:
        """Clear all cached models."""
        self._cache.clear()
        self._access_times.clear()
        self.logger.info("Model cache cleared")


class PredictionService(IPredictionService):
    """Main prediction service orchestrating all models."""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        self.model_cache = ModelCache(
            max_size=self.settings.ml.cache_size_mb // 100,  # Rough estimation
            ttl_seconds=3600
        )
        self._model_status: Dict[str, ModelStatus] = {}
        self._startup_time = time.time()
    
    async def initialize(self) -> None:
        """Initialize the prediction service."""
        self.logger.info("Initializing prediction service")
        
        # Load models asynchronously
        model_names = ["conversion_v2", "channel_v2", "nlp_v2"]
        
        for model_name in model_names:
            try:
                self._model_status[model_name] = ModelStatus.LOADING
                await self._load_model_async(model_name)
                self._model_status[model_name] = ModelStatus.READY
            except Exception as e:
                self.logger.error(f"Failed to load model {model_name}", error=str(e))
                self._model_status[model_name] = ModelStatus.ERROR
        
        self.logger.info("Prediction service initialized")
    
    async def _load_model_async(self, model_name: str) -> None:
        """Load model asynchronously."""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self._load_model, model_name)
    
    def _load_model(self, model_name: str) -> Any:
        """Load model from disk or create new instance."""
        try:
            # Try to load from disk first
            model_path = self.settings.models_dir / model_name / "latest"
            
            if model_path.exists():
                model_class = ModelRegistry.get_model_class(model_name)
                model = model_class()
                model.load(model_path)
                self.logger.info(f"Model {model_name} loaded from disk")
            else:
                # Create new model instance
                model_class = ModelRegistry.get_model_class(model_name)
                model = model_class()
                self.logger.info(f"New model {model_name} instance created")
            
            # Cache the model
            self.model_cache.put(model_name, model)
            return model
            
        except Exception as e:
            self.logger.error(f"Failed to load model {model_name}", error=str(e))
            raise ModelError(f"Failed to load model {model_name}: {e}")
    
    def _get_model(self, model_name: str, version: Optional[str] = None) -> Any:
        """Get model from cache or load it."""
        model = self.model_cache.get(model_name, version)
        
        if model is None:
            model = self._load_model(model_name)
        
        return model
    
    @log_performance("conversion_prediction")
    async def predict_conversion(self, request: ConversionRequest) -> ConversionResponse:
        """Predict conversion probability."""
        try:
            model = self._get_model("conversion_v2")
            
            if not model.is_trained:
                raise ModelNotTrainedError("Conversion model is not trained")
            
            # Prepare input data
            input_data = pd.DataFrame([{
                'prospect_id': request.prospect_id,
                'company_size': request.company_size,
                'industry': request.industry,
                'country': request.country,
                'nb_interactions': request.nb_interactions,
                'nb_calls': request.nb_calls,
                'nb_emails': request.nb_emails,
                'nb_meetings': request.nb_meetings,
                'response_speed': request.response_speed,
                'engagement_score': request.engagement_score,
                'declared_budget': request.declared_budget,
                'days_in_pipeline': request.days_in_pipeline
            }])
            
            # Make prediction
            start_time = time.time()
            
            if hasattr(model, 'predict_proba'):
                probabilities = model.predict_proba(input_data)
                conversion_prob = float(probabilities[0, 1])  # Probability of class 1
            else:
                predictions = model.predict(input_data)
                conversion_prob = float(predictions[0])
            
            conversion_prediction = int(conversion_prob > 0.5)
            confidence_score = max(conversion_prob, 1 - conversion_prob)
            
            processing_time = (time.time() - start_time) * 1000
            
            # Get feature importance for explanation
            feature_importance = model.get_feature_importance()
            
            # Generate insights
            risk_factors = self._identify_risk_factors(input_data.iloc[0], feature_importance)
            opportunities = self._identify_opportunities(input_data.iloc[0], feature_importance)
            
            return ConversionResponse(
                prospect_id=request.prospect_id,
                conversion_probability=conversion_prob,
                conversion_prediction=conversion_prediction,
                confidence_score=confidence_score,
                risk_factors=risk_factors,
                opportunities=opportunities,
                feature_importance=dict(list(feature_importance.items())[:10]),  # Top 10
                processing_time_ms=processing_time,
                model_version=model.version
            )
            
        except Exception as e:
            self.logger.error("Conversion prediction failed", error=str(e), prospect_id=request.prospect_id)
            raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")
    
    @log_performance("channel_optimization")
    async def optimize_channel(self, request: ChannelRequest) -> ChannelResponse:
        """Optimize communication channel and timing."""
        try:
            model = self._get_model("channel_v2")
            
            if not model.is_trained:
                raise ModelNotTrainedError("Channel model is not trained")
            
            # Prepare input data
            input_data = pd.DataFrame([{
                'prospect_id': request.prospect_id,
                'has_email': request.has_email,
                'has_phone': request.has_phone,
                'has_linkedin': request.has_linkedin,
                'email_response_rate': request.email_response_rate,
                'phone_response_rate': request.phone_response_rate,
                'meeting_acceptance_rate': request.meeting_acceptance_rate,
                'preferred_channel': request.preferred_channel.value if request.preferred_channel else None,
                'timezone': request.timezone,
                'business_hours_only': request.business_hours_only,
                'urgency_level': request.urgency_level,
                'message_type': request.message_type
            }])
            
            # Make prediction
            start_time = time.time()
            
            if hasattr(model, 'predict_channel_timing_separate'):
                channel_pred, timing_pred = model.predict_channel_timing_separate(input_data)
                optimal_channel = list(model.CHANNELS.keys())[channel_pred[0]]
                optimal_timing = list(model.TIMINGS.keys())[timing_pred[0]]
            else:
                predictions = model.predict(input_data)
                # Parse combined prediction
                if '_' in predictions[0]:
                    optimal_channel, optimal_timing = predictions[0].split('_', 1)
                else:
                    optimal_channel, optimal_timing = predictions[0], 'afternoon'
            
            processing_time = (time.time() - start_time) * 1000
            
            # Calculate confidence and alternatives
            confidence_score = 0.8  # Placeholder - would be calculated from model
            
            # Generate alternative channels
            alternatives = self._generate_channel_alternatives(
                request, optimal_channel, confidence_score
            )
            
            # Generate timing recommendations
            timing_recommendations = self._generate_timing_recommendations(
                request, optimal_timing
            )
            
            # Generate reasoning
            reasoning = self._generate_channel_reasoning(
                request, optimal_channel, optimal_timing
            )
            
            return ChannelResponse(
                prospect_id=request.prospect_id,
                optimal_channel=optimal_channel,
                optimal_timing=optimal_timing,
                confidence_score=confidence_score,
                alternative_channels=alternatives,
                timing_recommendations=timing_recommendations,
                reasoning=reasoning,
                processing_time_ms=processing_time,
                model_version=model.version
            )
            
        except Exception as e:
            self.logger.error("Channel optimization failed", error=str(e), prospect_id=request.prospect_id)
            raise HTTPException(status_code=500, detail=f"Optimization failed: {str(e)}")
    
    @log_performance("nlp_analysis")
    async def analyze_text(self, request: NLPRequest) -> NLPResponse:
        """Perform comprehensive NLP analysis."""
        try:
            model = self._get_model("nlp_v2")
            
            if not model.is_trained:
                raise ModelNotTrainedError("NLP model is not trained")
            
            # Prepare input data
            input_data = pd.DataFrame([{
                'content': request.content,
                'language': request.language
            }])
            
            # Make prediction
            start_time = time.time()
            results = model.predict(input_data)
            processing_time = (time.time() - start_time) * 1000
            
            # Parse results
            response_data = {
                'call_id': request.call_id,
                'content_length': len(request.content),
                'language_detected': request.language,
                'text_quality_score': self._calculate_text_quality(request.content),
                'processing_time_ms': processing_time,
                'model_version': model.version
            }
            
            # Add analysis results
            if request.analyze_sentiment and 'sentiment' in results:
                sentiment_data = results['sentiment'][0]
                response_data['sentiment'] = SentimentResult(
                    label=sentiment_data['label'],
                    score=sentiment_data['score']
                )
            
            if request.analyze_intent and 'intent' in results:
                response_data['intent'] = IntentResult(
                    intent=results['intent'][0],
                    confidence=0.8  # Placeholder
                )
            
            if request.analyze_topics and 'topics' in results:
                topic_name = results['topics'][0]
                topic_id = results.get('topic_labels', [-1])[0]
                response_data['topic'] = TopicResult(
                    topic_id=topic_id,
                    topic_name=topic_name,
                    confidence=0.7  # Placeholder
                )
            
            if request.analyze_urgency and 'urgency' in results:
                response_data['urgency_score'] = results['urgency'][0]
            
            if request.extract_keywords:
                response_data['keywords'] = self._extract_keywords(request.content)
            
            return NLPResponse(**response_data)
            
        except Exception as e:
            self.logger.error("NLP analysis failed", error=str(e))
            raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")
    
    def _identify_risk_factors(self, prospect_data: pd.Series, feature_importance: Dict[str, float]) -> List[str]:
        """Identify risk factors for conversion."""
        risk_factors = []
        
        if prospect_data.get('nb_interactions', 0) < 3:
            risk_factors.append("Low interaction count")
        
        if prospect_data.get('days_in_pipeline', 0) > 90:
            risk_factors.append("Long time in pipeline")
        
        if prospect_data.get('engagement_score', 0) < 0.3:
            risk_factors.append("Low engagement score")
        
        return risk_factors
    
    def _identify_opportunities(self, prospect_data: pd.Series, feature_importance: Dict[str, float]) -> List[str]:
        """Identify opportunities for conversion."""
        opportunities = []
        
        if prospect_data.get('engagement_score', 0) > 0.7:
            opportunities.append("High engagement - good conversion potential")
        
        if prospect_data.get('nb_meetings', 0) > 0:
            opportunities.append("Meeting scheduled - strong interest signal")
        
        return opportunities
    
    def _generate_channel_alternatives(self, request: ChannelRequest, optimal_channel: str, confidence: float) -> List[Dict[str, Any]]:
        """Generate alternative channel recommendations."""
        alternatives = []
        
        # Simple scoring based on availability and historical performance
        if request.has_email and optimal_channel != 'email':
            alternatives.append({
                'channel': 'email',
                'score': request.email_response_rate * 0.8
            })
        
        if request.has_phone and optimal_channel != 'phone':
            alternatives.append({
                'channel': 'phone',
                'score': request.phone_response_rate * 0.9
            })
        
        return sorted(alternatives, key=lambda x: x['score'], reverse=True)[:3]
    
    def _generate_timing_recommendations(self, request: ChannelRequest, optimal_timing: str) -> Dict[str, float]:
        """Generate timing recommendations."""
        # Simple heuristic based on urgency and business hours
        base_scores = {
            'morning': 0.7,
            'afternoon': 0.8,
            'evening': 0.4,
            'weekend': 0.2
        }
        
        # Adjust for urgency
        if request.urgency_level > 0.7:
            base_scores['morning'] += 0.2
            base_scores['afternoon'] += 0.1
        
        # Adjust for business hours preference
        if request.business_hours_only:
            base_scores['evening'] *= 0.5
            base_scores['weekend'] *= 0.3
        
        return base_scores
    
    def _generate_channel_reasoning(self, request: ChannelRequest, channel: str, timing: str) -> List[str]:
        """Generate reasoning for channel recommendation."""
        reasoning = []
        
        if channel == 'email':
            reasoning.append(f"Email recommended due to {request.email_response_rate:.1%} response rate")
        elif channel == 'phone':
            reasoning.append(f"Phone call recommended for direct engagement")
        
        if timing == 'morning':
            reasoning.append("Morning timing for better attention")
        elif timing == 'afternoon':
            reasoning.append("Afternoon timing for optimal availability")
        
        if request.urgency_level > 0.7:
            reasoning.append("High urgency requires immediate contact")
        
        return reasoning
    
    def _calculate_text_quality(self, text: str) -> float:
        """Calculate text quality score."""
        if not text:
            return 0.0
        
        # Simple quality metrics
        word_count = len(text.split())
        char_count = len(text)
        
        # Quality factors
        length_score = min(1.0, word_count / 50)  # Optimal around 50 words
        completeness_score = 1.0 if char_count > 20 else char_count / 20
        
        return (length_score + completeness_score) / 2
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text."""
        # Simple keyword extraction
        words = text.lower().split()
        
        # Filter common words
        stop_words = {'le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour'}
        keywords = [word for word in words if len(word) > 3 and word not in stop_words]
        
        # Return top 10 most frequent
        from collections import Counter
        word_counts = Counter(keywords)
        return [word for word, count in word_counts.most_common(10)]
    
    def get_model_status(self) -> Dict[str, ModelStatus]:
        """Get status of all models."""
        return self._model_status.copy()
    
    def get_uptime(self) -> float:
        """Get service uptime in seconds."""
        return time.time() - self._startup_time
    
    async def predict(self, model_name: str, input_data: Dict[str, Any], model_version: Optional[str] = None) -> PredictionResult:
        """Generic prediction interface."""
        # This would be implemented for the generic interface
        raise NotImplementedError("Use specific prediction methods")
    
    async def batch_predict(self, model_name: str, input_data: List[Dict[str, Any]], model_version: Optional[str] = None) -> List[PredictionResult]:
        """Batch prediction interface."""
        # This would be implemented for batch processing
        raise NotImplementedError("Batch prediction not yet implemented")
    
    def get_model_info(self, model_name: str) -> ModelInfo:
        """Get model information."""
        try:
            model = self._get_model(model_name)
            return model.get_info()
        except Exception as e:
            raise ModelNotFoundError(f"Model {model_name} not found: {e}")
    
    def list_models(self) -> List[ModelInfo]:
        """List all available models."""
        model_infos = []
        for model_name in ModelRegistry.list_models():
            try:
                info = self.get_model_info(model_name)
                model_infos.append(info)
            except Exception:
                continue
        return model_infos
