"""
Pydantic models for Sensei AI Suite v2.0 API.

Comprehensive request/response models with:
- Advanced validation
- Type safety
- Documentation
- Serialization optimization
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field, validator, root_validator
from pydantic.types import EmailStr, constr, confloat, conint


class ChannelType(str, Enum):
    """Available communication channels."""
    EMAIL = "email"
    PHONE = "phone"
    MEETING = "meeting"
    LINKEDIN = "linkedin"
    SMS = "sms"


class TimingType(str, Enum):
    """Available communication timings."""
    MORNING = "morning"
    AFTERNOON = "afternoon"
    EVENING = "evening"
    WEEKEND = "weekend"


class SentimentType(str, Enum):
    """Sentiment analysis results."""
    POSITIVE = "POSITIVE"
    NEGATIVE = "NEGATIVE"
    NEUTRAL = "NEUTRAL"


class IntentType(str, Enum):
    """Intent detection results."""
    QUESTION = "question"
    REQUEST = "request"
    COMPLAINT = "complaint"
    COMPLIMENT = "compliment"
    INFORMATION = "information"
    GENERAL = "general"
    UNKNOWN = "unknown"


class ModelStatus(str, Enum):
    """Model status types."""
    READY = "ready"
    LOADING = "loading"
    ERROR = "error"
    UPDATING = "updating"


# Base models
class BaseRequest(BaseModel):
    """Base request model with common fields."""
    request_id: Optional[str] = Field(None, description="Unique request identifier")
    timestamp: Optional[datetime] = Field(default_factory=datetime.utcnow, description="Request timestamp")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class BaseResponse(BaseModel):
    """Base response model with common fields."""
    success: bool = Field(True, description="Request success status")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")
    model_version: Optional[str] = Field(None, description="Model version used")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ErrorResponse(BaseResponse):
    """Error response model."""
    success: bool = Field(False, description="Request success status")
    error_code: str = Field(..., description="Error code")
    error_message: str = Field(..., description="Human-readable error message")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")


# Conversion prediction models
class ConversionRequest(BaseRequest):
    """Request model for conversion prediction."""
    prospect_id: constr(min_length=1, max_length=100) = Field(..., description="Unique prospect identifier")
    
    # Prospect information
    company_size: Optional[constr(regex=r"^(startup|small|medium|large|enterprise)$")] = Field(
        "medium", description="Company size category"
    )
    industry: Optional[str] = Field("technology", description="Industry sector")
    country: Optional[constr(min_length=2, max_length=3)] = Field("FR", description="Country code")
    
    # Interaction data
    nb_interactions: conint(ge=0, le=1000) = Field(0, description="Number of interactions")
    nb_calls: conint(ge=0, le=100) = Field(0, description="Number of calls")
    nb_emails: conint(ge=0, le=500) = Field(0, description="Number of emails")
    nb_meetings: conint(ge=0, le=50) = Field(0, description="Number of meetings")
    
    # Behavioral data
    response_speed: Optional[constr(regex=r"^(fast|medium|slow)$")] = Field(
        "medium", description="Response speed category"
    )
    engagement_score: confloat(ge=0.0, le=1.0) = Field(0.5, description="Engagement score")
    
    # Budget and timing
    declared_budget: Optional[constr(regex=r"^(small|medium|large|enterprise)$")] = Field(
        "medium", description="Declared budget category"
    )
    days_in_pipeline: conint(ge=0, le=365) = Field(0, description="Days in sales pipeline")
    
    @validator('prospect_id')
    def validate_prospect_id(cls, v):
        """Validate prospect ID format."""
        if not v or not v.strip():
            raise ValueError("Prospect ID cannot be empty")
        return v.strip()


class ConversionResponse(BaseResponse):
    """Response model for conversion prediction."""
    prospect_id: str = Field(..., description="Prospect identifier")
    conversion_probability: confloat(ge=0.0, le=1.0) = Field(..., description="Conversion probability")
    conversion_prediction: conint(ge=0, le=1) = Field(..., description="Binary conversion prediction")
    confidence_score: confloat(ge=0.0, le=1.0) = Field(..., description="Prediction confidence")
    risk_factors: List[str] = Field(default_factory=list, description="Identified risk factors")
    opportunities: List[str] = Field(default_factory=list, description="Identified opportunities")
    feature_importance: Dict[str, float] = Field(default_factory=dict, description="Feature importance scores")


# Channel optimization models
class ChannelRequest(BaseRequest):
    """Request model for channel optimization."""
    prospect_id: constr(min_length=1, max_length=100) = Field(..., description="Unique prospect identifier")
    
    # Contact information
    has_email: bool = Field(True, description="Has valid email address")
    has_phone: bool = Field(True, description="Has valid phone number")
    has_linkedin: bool = Field(False, description="Has LinkedIn profile")
    
    # Historical data
    email_response_rate: confloat(ge=0.0, le=1.0) = Field(0.1, description="Historical email response rate")
    phone_response_rate: confloat(ge=0.0, le=1.0) = Field(0.05, description="Historical phone response rate")
    meeting_acceptance_rate: confloat(ge=0.0, le=1.0) = Field(0.3, description="Historical meeting acceptance rate")
    
    # Preferences and behavior
    preferred_channel: Optional[ChannelType] = Field(None, description="Preferred communication channel")
    timezone: Optional[str] = Field("Europe/Paris", description="Prospect timezone")
    business_hours_only: bool = Field(True, description="Contact only during business hours")
    
    # Context
    urgency_level: confloat(ge=0.0, le=1.0) = Field(0.5, description="Communication urgency level")
    message_type: Optional[constr(regex=r"^(follow_up|proposal|meeting|information)$")] = Field(
        "follow_up", description="Type of message to send"
    )


class ChannelResponse(BaseResponse):
    """Response model for channel optimization."""
    prospect_id: str = Field(..., description="Prospect identifier")
    optimal_channel: ChannelType = Field(..., description="Recommended communication channel")
    optimal_timing: TimingType = Field(..., description="Recommended communication timing")
    confidence_score: confloat(ge=0.0, le=1.0) = Field(..., description="Recommendation confidence")
    alternative_channels: List[Dict[str, Union[ChannelType, float]]] = Field(
        default_factory=list, description="Alternative channels with scores"
    )
    timing_recommendations: Dict[str, float] = Field(
        default_factory=dict, description="Timing scores for different periods"
    )
    reasoning: List[str] = Field(default_factory=list, description="Reasoning behind recommendation")


# NLP analysis models
class NLPRequest(BaseRequest):
    """Request model for NLP analysis."""
    call_id: Optional[str] = Field(None, description="Call identifier")
    speaker_id: Optional[str] = Field(None, description="Speaker identifier")
    content: constr(min_length=10, max_length=5000) = Field(..., description="Text content to analyze")
    language: Optional[constr(regex=r"^(fr|en|es|de|it)$")] = Field("fr", description="Content language")
    
    # Analysis options
    analyze_sentiment: bool = Field(True, description="Perform sentiment analysis")
    analyze_intent: bool = Field(True, description="Perform intent detection")
    analyze_topics: bool = Field(True, description="Perform topic modeling")
    analyze_urgency: bool = Field(True, description="Analyze urgency level")
    extract_keywords: bool = Field(True, description="Extract key phrases")
    
    @validator('content')
    def validate_content(cls, v):
        """Validate content is not empty."""
        if not v or not v.strip():
            raise ValueError("Content cannot be empty")
        return v.strip()


class SentimentResult(BaseModel):
    """Sentiment analysis result."""
    label: SentimentType = Field(..., description="Sentiment label")
    score: confloat(ge=0.0, le=1.0) = Field(..., description="Sentiment confidence score")
    emotions: Optional[Dict[str, float]] = Field(None, description="Detailed emotion scores")


class IntentResult(BaseModel):
    """Intent detection result."""
    intent: IntentType = Field(..., description="Detected intent")
    confidence: confloat(ge=0.0, le=1.0) = Field(..., description="Intent confidence score")
    entities: List[Dict[str, Any]] = Field(default_factory=list, description="Extracted entities")


class TopicResult(BaseModel):
    """Topic modeling result."""
    topic_id: int = Field(..., description="Topic cluster ID")
    topic_name: str = Field(..., description="Topic name/label")
    confidence: confloat(ge=0.0, le=1.0) = Field(..., description="Topic assignment confidence")
    keywords: List[str] = Field(default_factory=list, description="Topic keywords")


class NLPResponse(BaseResponse):
    """Response model for NLP analysis."""
    call_id: Optional[str] = Field(None, description="Call identifier")
    content_length: int = Field(..., description="Analyzed content length")
    language_detected: str = Field(..., description="Detected language")
    
    # Analysis results
    sentiment: Optional[SentimentResult] = Field(None, description="Sentiment analysis result")
    intent: Optional[IntentResult] = Field(None, description="Intent detection result")
    topic: Optional[TopicResult] = Field(None, description="Topic modeling result")
    urgency_score: Optional[confloat(ge=0.0, le=1.0)] = Field(None, description="Urgency score")
    keywords: List[str] = Field(default_factory=list, description="Extracted keywords")
    
    # Quality metrics
    text_quality_score: confloat(ge=0.0, le=1.0) = Field(..., description="Text quality score")
    processing_notes: List[str] = Field(default_factory=list, description="Processing notes and warnings")


# Batch processing models
class BatchRequest(BaseRequest):
    """Base model for batch processing requests."""
    batch_id: Optional[str] = Field(None, description="Batch identifier")
    items: List[Dict[str, Any]] = Field(..., min_items=1, max_items=1000, description="Batch items")
    
    @validator('items')
    def validate_batch_size(cls, v):
        """Validate batch size."""
        if len(v) > 1000:
            raise ValueError("Batch size cannot exceed 1000 items")
        return v


class BatchResponse(BaseResponse):
    """Response model for batch processing."""
    batch_id: str = Field(..., description="Batch identifier")
    total_items: int = Field(..., description="Total number of items processed")
    successful_items: int = Field(..., description="Number of successfully processed items")
    failed_items: int = Field(..., description="Number of failed items")
    results: List[Dict[str, Any]] = Field(..., description="Processing results")
    errors: List[Dict[str, Any]] = Field(default_factory=list, description="Processing errors")


# Health and status models
class HealthResponse(BaseResponse):
    """Health check response."""
    status: str = Field(..., description="Service status")
    version: str = Field(..., description="Service version")
    uptime_seconds: float = Field(..., description="Service uptime in seconds")
    models_status: Dict[str, ModelStatus] = Field(..., description="Individual model statuses")
    system_metrics: Dict[str, Any] = Field(default_factory=dict, description="System metrics")


class ModelInfoResponse(BaseResponse):
    """Model information response."""
    model_name: str = Field(..., description="Model name")
    model_version: str = Field(..., description="Model version")
    model_type: str = Field(..., description="Model type")
    status: ModelStatus = Field(..., description="Model status")
    last_updated: datetime = Field(..., description="Last update timestamp")
    metrics: Dict[str, float] = Field(default_factory=dict, description="Model performance metrics")
    feature_count: int = Field(..., description="Number of features")
    training_samples: int = Field(..., description="Number of training samples")
