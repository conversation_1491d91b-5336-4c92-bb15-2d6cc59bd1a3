"""
Performance optimization utilities for Sensei AI Suite v2.0.

Features:
- Memory optimization and monitoring
- CPU profiling and optimization
- Caching strategies
- Parallel processing
- Resource management
"""

import gc
import time
import threading
import multiprocessing
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor
from functools import wraps, lru_cache
from typing import Any, Callable, Dict, List, Optional, Union
from contextlib import contextmanager
from dataclasses import dataclass
from pathlib import Path

import psutil
import numpy as np
import pandas as pd

from .config import get_settings
from .logging import get_logger


@dataclass
class PerformanceMetrics:
    """Performance metrics container."""
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    execution_time_ms: float
    cache_hit_rate: Optional[float] = None
    throughput_per_second: Optional[float] = None


class MemoryOptimizer:
    """Memory optimization utilities."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._memory_threshold_mb = 2000  # 2GB threshold
    
    @contextmanager
    def memory_monitor(self, operation_name: str):
        """Context manager for memory monitoring."""
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024
        
        self.logger.debug(f"Memory before {operation_name}: {initial_memory:.1f}MB")
        
        try:
            yield
        finally:
            final_memory = process.memory_info().rss / 1024 / 1024
            memory_diff = final_memory - initial_memory
            
            self.logger.debug(
                f"Memory after {operation_name}: {final_memory:.1f}MB (Δ{memory_diff:+.1f}MB)"
            )
            
            if final_memory > self._memory_threshold_mb:
                self.logger.warning(
                    f"High memory usage detected: {final_memory:.1f}MB",
                    operation=operation_name
                )
                self.cleanup_memory()
    
    def cleanup_memory(self):
        """Force garbage collection and memory cleanup."""
        self.logger.info("Performing memory cleanup")
        
        # Force garbage collection
        collected = gc.collect()
        
        # Get memory info after cleanup
        process = psutil.Process()
        memory_after = process.memory_info().rss / 1024 / 1024
        
        self.logger.info(
            f"Memory cleanup completed: {collected} objects collected, "
            f"memory usage: {memory_after:.1f}MB"
        )
    
    def optimize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Optimize DataFrame memory usage."""
        initial_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
        
        # Optimize numeric columns
        for col in df.select_dtypes(include=['int64']).columns:
            if df[col].min() >= 0:
                if df[col].max() < 255:
                    df[col] = df[col].astype('uint8')
                elif df[col].max() < 65535:
                    df[col] = df[col].astype('uint16')
                elif df[col].max() < 4294967295:
                    df[col] = df[col].astype('uint32')
            else:
                if df[col].min() > -128 and df[col].max() < 127:
                    df[col] = df[col].astype('int8')
                elif df[col].min() > -32768 and df[col].max() < 32767:
                    df[col] = df[col].astype('int16')
                elif df[col].min() > -2147483648 and df[col].max() < 2147483647:
                    df[col] = df[col].astype('int32')
        
        # Optimize float columns
        for col in df.select_dtypes(include=['float64']).columns:
            df[col] = pd.to_numeric(df[col], downcast='float')
        
        # Optimize object columns
        for col in df.select_dtypes(include=['object']).columns:
            if df[col].nunique() / len(df) < 0.5:  # If less than 50% unique values
                df[col] = df[col].astype('category')
        
        final_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
        reduction = (initial_memory - final_memory) / initial_memory * 100
        
        self.logger.info(
            f"DataFrame optimized: {initial_memory:.1f}MB → {final_memory:.1f}MB "
            f"({reduction:.1f}% reduction)"
        )
        
        return df


class CacheManager:
    """Advanced caching with performance monitoring."""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self._cache: Dict[str, Any] = {}
        self._access_times: Dict[str, float] = {}
        self._hit_count = 0
        self._miss_count = 0
        self.logger = get_logger(__name__)
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache."""
        if key in self._cache:
            self._access_times[key] = time.time()
            self._hit_count += 1
            return self._cache[key]
        else:
            self._miss_count += 1
            return None
    
    def put(self, key: str, value: Any) -> None:
        """Put item in cache."""
        if len(self._cache) >= self.max_size:
            self._evict_lru()
        
        self._cache[key] = value
        self._access_times[key] = time.time()
    
    def _evict_lru(self) -> None:
        """Evict least recently used item."""
        if self._access_times:
            lru_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
            del self._cache[lru_key]
            del self._access_times[lru_key]
    
    @property
    def hit_rate(self) -> float:
        """Calculate cache hit rate."""
        total = self._hit_count + self._miss_count
        return self._hit_count / total if total > 0 else 0.0
    
    def clear(self) -> None:
        """Clear cache."""
        self._cache.clear()
        self._access_times.clear()
        self._hit_count = 0
        self._miss_count = 0


class ParallelProcessor:
    """Parallel processing utilities."""
    
    def __init__(self, max_workers: Optional[int] = None):
        self.max_workers = max_workers or min(32, (multiprocessing.cpu_count() or 1) + 4)
        self.logger = get_logger(__name__)
    
    def process_parallel(
        self,
        func: Callable,
        items: List[Any],
        use_processes: bool = False,
        chunk_size: Optional[int] = None
    ) -> List[Any]:
        """Process items in parallel."""
        
        if len(items) == 0:
            return []
        
        # Determine optimal chunk size
        if chunk_size is None:
            chunk_size = max(1, len(items) // self.max_workers)
        
        executor_class = ProcessPoolExecutor if use_processes else ThreadPoolExecutor
        
        self.logger.info(
            f"Processing {len(items)} items in parallel",
            executor=executor_class.__name__,
            workers=self.max_workers,
            chunk_size=chunk_size
        )
        
        start_time = time.time()
        
        try:
            with executor_class(max_workers=self.max_workers) as executor:
                # Split items into chunks
                chunks = [items[i:i + chunk_size] for i in range(0, len(items), chunk_size)]
                
                # Process chunks
                futures = [executor.submit(self._process_chunk, func, chunk) for chunk in chunks]
                
                # Collect results
                results = []
                for future in futures:
                    results.extend(future.result())
                
                processing_time = time.time() - start_time
                throughput = len(items) / processing_time
                
                self.logger.info(
                    f"Parallel processing completed",
                    items=len(items),
                    time_seconds=processing_time,
                    throughput_per_second=throughput
                )
                
                return results
                
        except Exception as e:
            self.logger.error(f"Parallel processing failed: {e}")
            # Fallback to sequential processing
            return [func(item) for item in items]
    
    def _process_chunk(self, func: Callable, chunk: List[Any]) -> List[Any]:
        """Process a chunk of items."""
        return [func(item) for item in chunk]


class PerformanceProfiler:
    """Performance profiling and monitoring."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.memory_optimizer = MemoryOptimizer()
        self._profiles: Dict[str, List[PerformanceMetrics]] = {}
    
    def profile(self, operation_name: str):
        """Decorator for performance profiling."""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                return self._profile_execution(func, operation_name, *args, **kwargs)
            return wrapper
        return decorator
    
    def _profile_execution(self, func: Callable, operation_name: str, *args, **kwargs) -> Any:
        """Profile function execution."""
        
        # Initial metrics
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024
        initial_cpu = process.cpu_percent()
        start_time = time.time()
        
        try:
            with self.memory_optimizer.memory_monitor(operation_name):
                result = func(*args, **kwargs)
            
            # Final metrics
            end_time = time.time()
            final_memory = process.memory_info().rss / 1024 / 1024
            execution_time = (end_time - start_time) * 1000  # Convert to ms
            
            # Create metrics
            metrics = PerformanceMetrics(
                cpu_percent=process.cpu_percent(),
                memory_mb=final_memory,
                memory_percent=process.memory_percent(),
                execution_time_ms=execution_time
            )
            
            # Store metrics
            if operation_name not in self._profiles:
                self._profiles[operation_name] = []
            self._profiles[operation_name].append(metrics)
            
            # Log performance
            self.logger.info(
                f"Performance profile: {operation_name}",
                execution_time_ms=execution_time,
                memory_mb=final_memory,
                memory_delta_mb=final_memory - initial_memory
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Profiled operation failed: {operation_name}", error=str(e))
            raise
    
    def get_profile_summary(self, operation_name: str) -> Dict[str, float]:
        """Get performance profile summary."""
        
        if operation_name not in self._profiles:
            return {}
        
        metrics_list = self._profiles[operation_name]
        
        return {
            'count': len(metrics_list),
            'avg_execution_time_ms': np.mean([m.execution_time_ms for m in metrics_list]),
            'max_execution_time_ms': np.max([m.execution_time_ms for m in metrics_list]),
            'avg_memory_mb': np.mean([m.memory_mb for m in metrics_list]),
            'max_memory_mb': np.max([m.memory_mb for m in metrics_list]),
            'avg_cpu_percent': np.mean([m.cpu_percent for m in metrics_list])
        }
    
    def get_all_profiles(self) -> Dict[str, Dict[str, float]]:
        """Get all performance profiles."""
        return {
            operation: self.get_profile_summary(operation)
            for operation in self._profiles.keys()
        }


# Global instances
memory_optimizer = MemoryOptimizer()
cache_manager = CacheManager()
parallel_processor = ParallelProcessor()
performance_profiler = PerformanceProfiler()


# Decorators for easy use
def optimize_memory(func):
    """Decorator for memory optimization."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        with memory_optimizer.memory_monitor(func.__name__):
            return func(*args, **kwargs)
    return wrapper


def cache_result(ttl_seconds: int = 3600):
    """Decorator for result caching."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key
            key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # Try to get from cache
            result = cache_manager.get(key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_manager.put(key, result)
            
            return result
        return wrapper
    return decorator


def profile_performance(operation_name: Optional[str] = None):
    """Decorator for performance profiling."""
    def decorator(func):
        name = operation_name or func.__name__
        return performance_profiler.profile(name)(func)
    return decorator


def parallel_map(func: Callable, items: List[Any], use_processes: bool = False) -> List[Any]:
    """Parallel map function."""
    return parallel_processor.process_parallel(func, items, use_processes)


# System monitoring utilities
def get_system_metrics() -> Dict[str, float]:
    """Get current system metrics."""
    process = psutil.Process()
    
    return {
        'cpu_percent': psutil.cpu_percent(),
        'memory_percent': psutil.virtual_memory().percent,
        'disk_percent': psutil.disk_usage('/').percent,
        'process_memory_mb': process.memory_info().rss / 1024 / 1024,
        'process_cpu_percent': process.cpu_percent(),
        'load_average_1m': psutil.getloadavg()[0] if hasattr(psutil, 'getloadavg') else 0.0
    }


def check_resource_limits() -> Dict[str, bool]:
    """Check if system resources are within acceptable limits."""
    metrics = get_system_metrics()
    
    return {
        'cpu_ok': metrics['cpu_percent'] < 80,
        'memory_ok': metrics['memory_percent'] < 85,
        'disk_ok': metrics['disk_percent'] < 90,
        'process_memory_ok': metrics['process_memory_mb'] < 4000  # 4GB limit
    }


@contextmanager
def resource_monitor(operation_name: str, alert_threshold: Dict[str, float] = None):
    """Context manager for resource monitoring with alerts."""
    
    if alert_threshold is None:
        alert_threshold = {
            'cpu_percent': 90,
            'memory_percent': 90,
            'execution_time_seconds': 300  # 5 minutes
        }
    
    logger = get_logger(__name__)
    start_time = time.time()
    start_metrics = get_system_metrics()
    
    logger.info(f"Starting resource monitoring for: {operation_name}")
    
    try:
        yield
    finally:
        end_time = time.time()
        end_metrics = get_system_metrics()
        execution_time = end_time - start_time
        
        # Check thresholds
        alerts = []
        
        if end_metrics['cpu_percent'] > alert_threshold['cpu_percent']:
            alerts.append(f"High CPU usage: {end_metrics['cpu_percent']:.1f}%")
        
        if end_metrics['memory_percent'] > alert_threshold['memory_percent']:
            alerts.append(f"High memory usage: {end_metrics['memory_percent']:.1f}%")
        
        if execution_time > alert_threshold['execution_time_seconds']:
            alerts.append(f"Long execution time: {execution_time:.1f}s")
        
        # Log results
        if alerts:
            logger.warning(
                f"Resource alerts for {operation_name}",
                alerts=alerts,
                execution_time=execution_time,
                final_metrics=end_metrics
            )
        else:
            logger.info(
                f"Resource monitoring completed for {operation_name}",
                execution_time=execution_time,
                cpu_usage=end_metrics['cpu_percent'],
                memory_usage=end_metrics['memory_percent']
            )
