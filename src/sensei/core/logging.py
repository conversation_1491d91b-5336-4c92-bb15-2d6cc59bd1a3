"""
Advanced logging system for Sensei AI Suite v2.0.

Features:
- Structured logging with JSON output
- Performance monitoring
- Error tracking with context
- Audit logging for compliance
- Distributed tracing support
"""

import logging
import sys
import time
import traceback
import uuid
from contextlib import contextmanager
from typing import Any, Dict, Optional, Union
from datetime import datetime
from functools import wraps

import structlog
from structlog.typing import Processor

from .config import get_settings


class PerformanceLogger:
    """Performance monitoring logger."""
    
    def __init__(self, logger: structlog.BoundLogger):
        self.logger = logger
        self.start_time = None
        self.operation_name = None
    
    def start(self, operation: str, **context):
        """Start performance monitoring."""
        self.operation_name = operation
        self.start_time = time.time()
        self.logger.info(
            "Operation started",
            operation=operation,
            **context
        )
    
    def end(self, **context):
        """End performance monitoring."""
        if self.start_time:
            duration = time.time() - self.start_time
            self.logger.info(
                "Operation completed",
                operation=self.operation_name,
                duration_ms=round(duration * 1000, 2),
                **context
            )
            self.start_time = None


class AuditLogger:
    """Audit logger for compliance."""
    
    def __init__(self, logger: structlog.BoundLogger):
        self.logger = logger
    
    def log_data_access(
        self,
        user_id: str,
        resource: str,
        action: str,
        success: bool = True,
        **context
    ):
        """Log data access for audit."""
        self.logger.info(
            "Data access audit",
            audit_type="data_access",
            user_id=user_id,
            resource=resource,
            action=action,
            success=success,
            timestamp=datetime.utcnow().isoformat(),
            **context
        )
    
    def log_model_operation(
        self,
        user_id: str,
        model_name: str,
        operation: str,
        success: bool = True,
        **context
    ):
        """Log model operations for audit."""
        self.logger.info(
            "Model operation audit",
            audit_type="model_operation",
            user_id=user_id,
            model_name=model_name,
            operation=operation,
            success=success,
            timestamp=datetime.utcnow().isoformat(),
            **context
        )


def add_trace_id(logger, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Add trace ID to log events."""
    if "trace_id" not in event_dict:
        event_dict["trace_id"] = str(uuid.uuid4())
    return event_dict


def add_service_metadata(logger, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Add service metadata to log events."""
    settings = get_settings()
    event_dict.update({
        "service": settings.project_name,
        "version": settings.version,
        "environment": settings.environment.value
    })
    return event_dict


def add_performance_metrics(logger, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Add performance metrics to log events."""
    import psutil
    import os
    
    # Add system metrics
    event_dict["system"] = {
        "cpu_percent": psutil.cpu_percent(),
        "memory_percent": psutil.virtual_memory().percent,
        "pid": os.getpid()
    }
    return event_dict


def configure_logging(
    level: Optional[str] = None,
    json_logs: Optional[bool] = None,
    enable_performance: bool = True,
    enable_audit: bool = True
) -> None:
    """
    Configure advanced logging system.
    
    Args:
        level: Log level (DEBUG, INFO, WARNING, ERROR)
        json_logs: Use JSON format for logs
        enable_performance: Enable performance monitoring
        enable_audit: Enable audit logging
    """
    settings = get_settings()
    
    # Use settings if not provided
    if level is None:
        level = settings.log_level.value
    if json_logs is None:
        json_logs = settings.is_production()
    
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    # Base processors
    processors: list[Processor] = [
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.dev.set_exc_info,
        structlog.processors.TimeStamper(fmt="iso"),
        add_trace_id,
        add_service_metadata,
    ]
    
    # Add performance monitoring if enabled
    if enable_performance:
        processors.append(add_performance_metrics)
    
    # Choose renderer based on environment
    if json_logs:
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.append(structlog.dev.ConsoleRenderer(colors=True))
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(log_level),
        logger_factory=structlog.WriteLoggerFactory(file=sys.stdout),
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=log_level,
    )
    
    # Suppress noisy third-party loggers
    logging.getLogger("google").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("transformers").setLevel(logging.WARNING)


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a structured logger."""
    return structlog.get_logger(name)


def get_performance_logger(name: str) -> PerformanceLogger:
    """Get a performance logger."""
    logger = get_logger(name)
    return PerformanceLogger(logger)


def get_audit_logger(name: str) -> AuditLogger:
    """Get an audit logger."""
    logger = get_logger(name)
    return AuditLogger(logger)


@contextmanager
def log_performance(operation: str, logger: Optional[structlog.BoundLogger] = None, **context):
    """Context manager for performance logging."""
    if logger is None:
        logger = get_logger("performance")
    
    perf_logger = PerformanceLogger(logger)
    perf_logger.start(operation, **context)
    try:
        yield perf_logger
    except Exception as e:
        logger.error(
            "Operation failed",
            operation=operation,
            error=str(e),
            error_type=type(e).__name__,
            **context
        )
        raise
    finally:
        perf_logger.end(**context)


def log_exception(
    logger: structlog.BoundLogger,
    exception: Exception,
    context: Optional[Dict[str, Any]] = None
):
    """Log exception with full context."""
    context = context or {}
    logger.error(
        "Exception occurred",
        error=str(exception),
        error_type=type(exception).__name__,
        traceback=traceback.format_exc(),
        **context
    )


def performance_monitor(operation: Optional[str] = None):
    """Decorator for performance monitoring."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation or f"{func.__module__}.{func.__name__}"
            logger = get_logger(func.__module__)
            
            with log_performance(op_name, logger):
                return func(*args, **kwargs)
        return wrapper
    return decorator


# Auto-configure logging on import
def _auto_configure():
    """Auto-configure logging based on environment."""
    try:
        configure_logging()
    except Exception:
        # Fallback to basic logging if configuration fails
        logging.basicConfig(level=logging.INFO)


# Initialize logging
_auto_configure()
