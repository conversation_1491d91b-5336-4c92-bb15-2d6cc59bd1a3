"""
Interfaces and abstract base classes for Sensei AI Suite v2.0.

Defines contracts for all major components to ensure consistency
and enable dependency injection and testing.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime
from pathlib import Path

import pandas as pd
import numpy as np
from pydantic import BaseModel


class ModelMetrics(BaseModel):
    """Model performance metrics."""
    accuracy: Optional[float] = None
    precision: Optional[float] = None
    recall: Optional[float] = None
    f1_score: Optional[float] = None
    auc_roc: Optional[float] = None
    auc_pr: Optional[float] = None
    log_loss: Optional[float] = None
    training_time: Optional[float] = None
    prediction_time: Optional[float] = None
    model_size_mb: Optional[float] = None
    feature_count: Optional[int] = None
    sample_count: Optional[int] = None


class ModelInfo(BaseModel):
    """Model information and metadata."""
    name: str
    version: str
    algorithm: str
    created_at: datetime
    updated_at: datetime
    metrics: ModelMetrics
    hyperparameters: Dict[str, Any]
    feature_names: List[str]
    target_name: str
    model_path: Optional[Path] = None
    description: Optional[str] = None
    tags: List[str] = []


class PredictionResult(BaseModel):
    """Prediction result with metadata."""
    prediction: Union[float, int, str, List[float]]
    confidence: float
    model_info: ModelInfo
    feature_values: Dict[str, Any]
    prediction_time: float
    timestamp: datetime


class IModel(ABC):
    """Interface for all ML models."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Model name."""
        pass
    
    @property
    @abstractmethod
    def version(self) -> str:
        """Model version."""
        pass
    
    @property
    @abstractmethod
    def is_trained(self) -> bool:
        """Check if model is trained."""
        pass
    
    @abstractmethod
    def train(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        X_val: Optional[pd.DataFrame] = None,
        y_val: Optional[pd.Series] = None,
        **kwargs
    ) -> ModelMetrics:
        """Train the model."""
        pass
    
    @abstractmethod
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions."""
        pass
    
    @abstractmethod
    def predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Predict probabilities."""
        pass
    
    @abstractmethod
    def save(self, path: Path) -> None:
        """Save model to disk."""
        pass
    
    @abstractmethod
    def load(self, path: Path) -> None:
        """Load model from disk."""
        pass
    
    @abstractmethod
    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance scores."""
        pass
    
    @abstractmethod
    def get_info(self) -> ModelInfo:
        """Get model information."""
        pass


class IDataSource(ABC):
    """Interface for data sources."""
    
    @abstractmethod
    def connect(self) -> None:
        """Connect to data source."""
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """Disconnect from data source."""
        pass
    
    @abstractmethod
    def query(self, sql: str, **kwargs) -> pd.DataFrame:
        """Execute query and return DataFrame."""
        pass
    
    @abstractmethod
    def get_schema(self, table_name: str) -> Dict[str, str]:
        """Get table schema."""
        pass
    
    @abstractmethod
    def validate_query(self, sql: str) -> bool:
        """Validate SQL query."""
        pass


class IFeatureStore(ABC):
    """Interface for feature store."""
    
    @abstractmethod
    def get_features(
        self,
        feature_names: List[str],
        entity_ids: List[str],
        timestamp: Optional[datetime] = None
    ) -> pd.DataFrame:
        """Get features for entities."""
        pass
    
    @abstractmethod
    def store_features(
        self,
        features: pd.DataFrame,
        feature_group: str,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Store features."""
        pass
    
    @abstractmethod
    def get_feature_metadata(self, feature_name: str) -> Dict[str, Any]:
        """Get feature metadata."""
        pass
    
    @abstractmethod
    def validate_features(self, features: pd.DataFrame) -> bool:
        """Validate feature data."""
        pass
    
    @abstractmethod
    def detect_drift(
        self,
        reference_data: pd.DataFrame,
        current_data: pd.DataFrame
    ) -> Dict[str, float]:
        """Detect feature drift."""
        pass


class IPredictionService(ABC):
    """Interface for prediction service."""
    
    @abstractmethod
    def predict(
        self,
        model_name: str,
        input_data: Dict[str, Any],
        model_version: Optional[str] = None
    ) -> PredictionResult:
        """Make prediction."""
        pass
    
    @abstractmethod
    def batch_predict(
        self,
        model_name: str,
        input_data: List[Dict[str, Any]],
        model_version: Optional[str] = None
    ) -> List[PredictionResult]:
        """Make batch predictions."""
        pass
    
    @abstractmethod
    def get_model_info(self, model_name: str) -> ModelInfo:
        """Get model information."""
        pass
    
    @abstractmethod
    def list_models(self) -> List[ModelInfo]:
        """List available models."""
        pass


class IHyperparameterOptimizer(ABC):
    """Interface for hyperparameter optimization."""
    
    @abstractmethod
    def optimize(
        self,
        model: IModel,
        X: pd.DataFrame,
        y: pd.Series,
        param_space: Dict[str, Any],
        n_trials: int = 100
    ) -> Dict[str, Any]:
        """Optimize hyperparameters."""
        pass
    
    @abstractmethod
    def get_best_params(self) -> Dict[str, Any]:
        """Get best parameters from last optimization."""
        pass
    
    @abstractmethod
    def get_optimization_history(self) -> List[Dict[str, Any]]:
        """Get optimization history."""
        pass


class IModelRegistry(ABC):
    """Interface for model registry."""
    
    @abstractmethod
    def register_model(self, model: IModel, metadata: Dict[str, Any]) -> str:
        """Register a model."""
        pass
    
    @abstractmethod
    def get_model(self, name: str, version: Optional[str] = None) -> IModel:
        """Get a model."""
        pass
    
    @abstractmethod
    def list_models(self) -> List[ModelInfo]:
        """List all models."""
        pass
    
    @abstractmethod
    def delete_model(self, name: str, version: Optional[str] = None) -> None:
        """Delete a model."""
        pass
    
    @abstractmethod
    def promote_model(self, name: str, version: str, stage: str) -> None:
        """Promote model to stage (staging/production)."""
        pass
