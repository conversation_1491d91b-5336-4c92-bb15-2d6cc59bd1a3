"""
Configuration management for Sensei AI Suite v2.0.

Unified configuration system with:
- Pydantic validation
- Environment-specific settings
- Secret management integration
- Type safety and documentation
"""

import os
from enum import Enum
from functools import lru_cache
from pathlib import Path
from typing import Dict, List, Optional, Union

try:
    from pydantic_settings import BaseSettings
    from pydantic import Field, validator
except ImportError:
    # Fallback for older pydantic versions
    from pydantic import BaseSettings, Field, validator
from pydantic.env_settings import SettingsSourceCallable


class Environment(str, Enum):
    """Environment types."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


class LogLevel(str, Enum):
    """Log levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class DatabaseConfig(BaseSettings):
    """Database configuration."""
    project_id: str = Field(default="datalake-sensei", description="GCP Project ID")
    dataset_id: str = Field(default="sensei_data", description="BigQuery dataset")
    location: str = Field(default="europe-west1", description="Data location")
    timeout: int = Field(default=300, description="Query timeout in seconds")
    max_bytes_billed: int = Field(default=20 * 1024**3, description="Max bytes per query")
    
    class Config:
        env_prefix = "DB_"


class APIConfig(BaseSettings):
    """API configuration."""
    host: str = Field(default="0.0.0.0", description="API host")
    port: int = Field(default=8000, description="API port")
    workers: int = Field(default=1, description="Number of workers")
    reload: bool = Field(default=False, description="Auto-reload on changes")
    log_level: LogLevel = Field(default=LogLevel.INFO, description="Log level")
    cors_origins: List[str] = Field(default=["*"], description="CORS origins")
    enable_docs: bool = Field(default=True, description="Enable API docs")
    rate_limit: int = Field(default=1000, description="Requests per minute")
    
    class Config:
        env_prefix = "API_"


class MLConfig(BaseSettings):
    """Machine Learning configuration."""
    max_model_versions: int = Field(default=5, description="Max model versions to keep")
    model_timeout: int = Field(default=30, description="Model prediction timeout")
    cache_size_mb: int = Field(default=2000, description="Model cache size in MB")
    auto_cleanup: bool = Field(default=True, description="Auto cleanup old models")
    ensemble_voting: str = Field(default="soft", description="Ensemble voting strategy")
    hyperopt_trials: int = Field(default=100, description="Hyperparameter optimization trials")
    cv_folds: int = Field(default=5, description="Cross-validation folds")
    early_stopping_rounds: int = Field(default=50, description="Early stopping rounds")
    
    class Config:
        env_prefix = "ML_"


class FeatureStoreConfig(BaseSettings):
    """Feature store configuration."""
    cache_ttl: int = Field(default=3600, description="Cache TTL in seconds")
    batch_size: int = Field(default=1000, description="Batch processing size")
    enable_drift_detection: bool = Field(default=True, description="Enable drift detection")
    drift_threshold: float = Field(default=0.1, description="Drift detection threshold")
    feature_validation: bool = Field(default=True, description="Enable feature validation")
    
    class Config:
        env_prefix = "FEATURE_"


class SecurityConfig(BaseSettings):
    """Security configuration."""
    secret_key: str = Field(description="Secret key for encryption")
    algorithm: str = Field(default="HS256", description="JWT algorithm")
    access_token_expire_minutes: int = Field(default=30, description="Token expiration")
    enable_audit: bool = Field(default=True, description="Enable audit logging")
    pii_hash_salt: str = Field(description="Salt for PII hashing")
    
    class Config:
        env_prefix = "SECURITY_"


class MonitoringConfig(BaseSettings):
    """Monitoring configuration."""
    enable_metrics: bool = Field(default=True, description="Enable metrics collection")
    metrics_port: int = Field(default=9090, description="Metrics server port")
    enable_tracing: bool = Field(default=False, description="Enable distributed tracing")
    log_sampling_rate: float = Field(default=1.0, description="Log sampling rate")
    
    class Config:
        env_prefix = "MONITORING_"


class Settings(BaseSettings):
    """Main settings class."""
    
    # Environment
    environment: Environment = Field(default=Environment.DEVELOPMENT)
    debug: bool = Field(default=False, description="Debug mode")
    log_level: LogLevel = Field(default=LogLevel.INFO)
    
    # Project metadata
    project_name: str = Field(default="sensei-ai-v2", description="Project name")
    version: str = Field(default="2.0.0", description="Project version")
    description: str = Field(default="Sensei AI Suite v2.0", description="Project description")
    
    # Sub-configurations
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    api: APIConfig = Field(default_factory=APIConfig)
    ml: MLConfig = Field(default_factory=MLConfig)
    feature_store: FeatureStoreConfig = Field(default_factory=FeatureStoreConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    
    # Paths
    models_dir: Path = Field(default=Path("models"), description="Models directory")
    data_dir: Path = Field(default=Path("data"), description="Data directory")
    logs_dir: Path = Field(default=Path("logs"), description="Logs directory")
    
    @validator("environment", pre=True)
    def validate_environment(cls, v):
        """Validate environment from string."""
        if isinstance(v, str):
            return Environment(v.lower())
        return v
    
    @validator("models_dir", "data_dir", "logs_dir", pre=True)
    def validate_paths(cls, v):
        """Ensure paths are Path objects."""
        if isinstance(v, str):
            return Path(v)
        return v
    
    def is_production(self) -> bool:
        """Check if running in production."""
        return self.environment == Environment.PRODUCTION
    
    def is_development(self) -> bool:
        """Check if running in development."""
        return self.environment == Environment.DEVELOPMENT
    
    def is_testing(self) -> bool:
        """Check if running in testing."""
        return self.environment == Environment.TESTING
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        @classmethod
        def customise_sources(
            cls,
            init_settings: SettingsSourceCallable,
            env_settings: SettingsSourceCallable,
            file_secret_settings: SettingsSourceCallable,
        ) -> tuple[SettingsSourceCallable, ...]:
            """Customize settings sources priority."""
            return (
                init_settings,
                env_settings,
                file_secret_settings,
            )


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()


# Global settings instance
settings = get_settings()
