"""
Custom exceptions for Sensei AI Suite v2.0.

Provides a hierarchy of exceptions for better error handling and debugging.
"""

from typing import Any, Dict, Optional


class SenseiException(Exception):
    """Base exception for all Sensei AI Suite errors."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize Sensei exception.
        
        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            details: Additional error details
            cause: Original exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.cause = cause
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary."""
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details,
            "cause": str(self.cause) if self.cause else None
        }


class ConfigError(SenseiException):
    """Configuration-related errors."""
    pass


class DataError(SenseiException):
    """Data access and processing errors."""
    pass


class ModelError(SenseiException):
    """Model training and prediction errors."""
    pass


class FeatureError(SenseiException):
    """Feature engineering and validation errors."""
    pass


class APIError(SenseiException):
    """API-related errors."""
    pass


class SecurityError(SenseiException):
    """Security and authentication errors."""
    pass


class ValidationError(SenseiException):
    """Data validation errors."""
    pass


class ResourceError(SenseiException):
    """Resource allocation and management errors."""
    pass


class TimeoutError(SenseiException):
    """Timeout-related errors."""
    pass


class NotFoundError(SenseiException):
    """Resource not found errors."""
    pass


class ConflictError(SenseiException):
    """Resource conflict errors."""
    pass


class RateLimitError(SenseiException):
    """Rate limiting errors."""
    pass


# Specific model errors
class ModelNotFoundError(ModelError):
    """Model not found error."""
    pass


class ModelNotTrainedError(ModelError):
    """Model not trained error."""
    pass


class ModelVersionError(ModelError):
    """Model version error."""
    pass


class HyperparameterError(ModelError):
    """Hyperparameter optimization error."""
    pass


# Specific data errors
class DataSourceError(DataError):
    """Data source connection error."""
    pass


class DataQualityError(DataError):
    """Data quality validation error."""
    pass


class DataSchemaError(DataError):
    """Data schema validation error."""
    pass


class BigQueryError(DataError):
    """BigQuery-specific error."""
    pass


# Specific feature errors
class FeatureDriftError(FeatureError):
    """Feature drift detection error."""
    pass


class FeatureValidationError(FeatureError):
    """Feature validation error."""
    pass


class FeatureStoreError(FeatureError):
    """Feature store error."""
    pass
