"""
Sensei AI Suite v2.0 - Core Module

This module contains the core components of the Sensei AI Suite:
- Configuration management
- Base classes and interfaces
- Common utilities and patterns
"""

__version__ = "2.0.0"
__author__ = "Sensei AI Team"

# Configuration import with fallback
try:
    from .config import Settings, get_settings
except ImportError:
    # Fallback for missing dependencies
    Settings = None
    get_settings = lambda: None
from .exceptions import SenseiException, ModelError, DataError, ConfigError
from .interfaces import IModel, IDataSource, IFeatureStore, IPredictionService

__all__ = [
    "Settings",
    "get_settings", 
    "SenseiException",
    "ModelError",
    "DataError", 
    "ConfigError",
    "IModel",
    "IDataSource",
    "IFeatureStore",
    "IPredictionService"
]
