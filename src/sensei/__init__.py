"""
Sensei AI Suite v2.0 - Next-Generation ML Platform for B2B Sales Intelligence.

Advanced features:
1. Ensemble Conversion Prediction (XGBoost + LightGBM + CatBoost)
2. Multi-modal Channel Optimization (Deep Learning + Traditional ML)
3. Transformer-based NLP Analysis (BERT/RoBERTa + Advanced Topic Modeling)
4. Real-time Feature Store with Drift Detection
5. Production-ready API with Auto-scaling
"""

__version__ = "2.0.0"
__author__ = "Sensei AI Team"

# Core modules
from .core import config, logging, exceptions, interfaces, performance

# Optional model imports (only if dependencies are available)
try:
    from .models import BaseModelV2, ModelRegistry
    __all__ = ["config", "logging", "exceptions", "interfaces", "performance", "BaseModelV2", "ModelRegistry"]
except ImportError:
    # Models not available due to missing dependencies
    __all__ = ["config", "logging", "exceptions", "interfaces", "performance"]
