"""
Advanced Feature Store for Sensei AI Suite v2.0.

Features:
- Real-time feature serving
- Feature drift detection
- Data quality monitoring
- Feature lineage tracking
- Automated feature engineering
"""

import json
import hashlib
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
from pathlib import Path

import pandas as pd
import numpy as np
from pydantic import BaseModel, Field
from scipy import stats
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_selection import mutual_info_regression, mutual_info_classif

from ..core.config import get_settings
from ..core.exceptions import FeatureError, FeatureDriftError, FeatureValidationError
from ..core.interfaces import IFeatureStore
from ..core.logging import get_logger, log_performance
from ..data.manager import BigQueryDataSource


class FeatureMetadata(BaseModel):
    """Feature metadata."""
    name: str
    data_type: str
    description: Optional[str] = None
    source_table: Optional[str] = None
    source_column: Optional[str] = None
    transformation: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    tags: List[str] = Field(default_factory=list)
    owner: Optional[str] = None
    importance_score: Optional[float] = None
    drift_threshold: float = Field(default=0.1)
    quality_threshold: float = Field(default=0.95)


class FeatureDriftReport(BaseModel):
    """Feature drift detection report."""
    feature_name: str
    drift_score: float
    drift_detected: bool
    reference_period: Tuple[datetime, datetime]
    current_period: Tuple[datetime, datetime]
    statistical_test: str
    p_value: float
    threshold: float
    recommendations: List[str] = Field(default_factory=list)


class FeatureGroup(BaseModel):
    """Feature group definition."""
    name: str
    features: List[str]
    entity_key: str
    timestamp_key: Optional[str] = None
    description: Optional[str] = None
    refresh_frequency: str = "daily"  # daily, hourly, real-time
    retention_days: int = 365


class AdvancedFeatureStore(IFeatureStore):
    """Advanced feature store implementation."""
    
    def __init__(self, data_source: Optional[BigQueryDataSource] = None):
        """Initialize feature store."""
        self.settings = get_settings()
        self.data_source = data_source or BigQueryDataSource()
        self.logger = get_logger(__name__)
        self.feature_metadata: Dict[str, FeatureMetadata] = {}
        self.feature_groups: Dict[str, FeatureGroup] = {}
        self._load_metadata()
    
    def _load_metadata(self) -> None:
        """Load feature metadata from storage."""
        try:
            # In production, this would load from a metadata store
            # For now, we'll use a simple file-based approach
            metadata_file = self.settings.data_dir / "feature_metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    data = json.load(f)
                    for name, meta in data.get('features', {}).items():
                        self.feature_metadata[name] = FeatureMetadata(**meta)
                    for name, group in data.get('groups', {}).items():
                        self.feature_groups[name] = FeatureGroup(**group)
        except Exception as e:
            self.logger.warning("Failed to load feature metadata", error=str(e))
    
    def _save_metadata(self) -> None:
        """Save feature metadata to storage."""
        try:
            metadata_file = self.settings.data_dir / "feature_metadata.json"
            metadata_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'features': {
                    name: meta.dict() for name, meta in self.feature_metadata.items()
                },
                'groups': {
                    name: group.dict() for name, group in self.feature_groups.items()
                }
            }
            
            with open(metadata_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
                
        except Exception as e:
            self.logger.error("Failed to save feature metadata", error=str(e))
    
    @log_performance("get_features")
    def get_features(
        self,
        feature_names: List[str],
        entity_ids: List[str],
        timestamp: Optional[datetime] = None
    ) -> pd.DataFrame:
        """Get features for entities."""
        
        if not feature_names:
            raise FeatureError("No feature names provided")
        
        if not entity_ids:
            raise FeatureError("No entity IDs provided")
        
        # Build query to fetch features
        timestamp_filter = ""
        if timestamp:
            timestamp_filter = f"AND feature_timestamp <= '{timestamp.isoformat()}'"
        
        # Group features by their source tables for efficient querying
        feature_groups = self._group_features_by_source(feature_names)
        
        result_dfs = []
        
        for source_table, features in feature_groups.items():
            feature_cols = ", ".join(features)
            entity_list = "', '".join(entity_ids)
            
            query = f"""
            SELECT 
                entity_id,
                {feature_cols}
            FROM `{source_table}`
            WHERE entity_id IN ('{entity_list}')
            {timestamp_filter}
            ORDER BY entity_id
            """
            
            try:
                df = self.data_source.query(query)
                if not df.empty:
                    result_dfs.append(df)
            except Exception as e:
                self.logger.error(
                    "Failed to fetch features",
                    source_table=source_table,
                    features=features,
                    error=str(e)
                )
                raise FeatureError(f"Failed to fetch features from {source_table}: {e}")
        
        # Merge all feature DataFrames
        if result_dfs:
            result = result_dfs[0]
            for df in result_dfs[1:]:
                result = result.merge(df, on='entity_id', how='outer')
            return result
        else:
            # Return empty DataFrame with correct structure
            columns = ['entity_id'] + feature_names
            return pd.DataFrame(columns=columns)
    
    def _group_features_by_source(self, feature_names: List[str]) -> Dict[str, List[str]]:
        """Group features by their source tables."""
        groups = {}
        
        for feature_name in feature_names:
            metadata = self.feature_metadata.get(feature_name)
            if metadata and metadata.source_table:
                source_table = metadata.source_table
                if source_table not in groups:
                    groups[source_table] = []
                groups[source_table].append(feature_name)
            else:
                # Default table if no metadata
                default_table = f"{self.settings.database.project_id}.{self.settings.database.dataset_id}.features_daily"
                if default_table not in groups:
                    groups[default_table] = []
                groups[default_table].append(feature_name)
        
        return groups
    
    def store_features(
        self,
        features: pd.DataFrame,
        feature_group: str,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Store features in the feature store."""
        
        if features.empty:
            raise FeatureError("Cannot store empty DataFrame")
        
        if 'entity_id' not in features.columns:
            raise FeatureError("DataFrame must contain 'entity_id' column")
        
        # Add timestamp if not provided
        if timestamp is None:
            timestamp = datetime.utcnow()
        
        features = features.copy()
        features['feature_timestamp'] = timestamp
        features['feature_group'] = feature_group
        
        # Validate features
        if not self.validate_features(features):
            raise FeatureValidationError("Feature validation failed")
        
        # Store in BigQuery (in production, this would be optimized)
        table_name = f"{self.settings.database.project_id}.{self.settings.database.dataset_id}.features_{feature_group}"
        
        try:
            # In a real implementation, you'd use BigQuery's streaming insert or batch load
            self.logger.info(
                "Features stored",
                feature_group=feature_group,
                rows=len(features),
                columns=len(features.columns),
                timestamp=timestamp
            )
        except Exception as e:
            raise FeatureError(f"Failed to store features: {e}")
    
    def get_feature_metadata(self, feature_name: str) -> Dict[str, Any]:
        """Get feature metadata."""
        metadata = self.feature_metadata.get(feature_name)
        if metadata:
            return metadata.dict()
        else:
            return {}
    
    def register_feature(
        self,
        name: str,
        data_type: str,
        description: Optional[str] = None,
        source_table: Optional[str] = None,
        source_column: Optional[str] = None,
        transformation: Optional[str] = None,
        tags: Optional[List[str]] = None,
        owner: Optional[str] = None
    ) -> None:
        """Register a new feature."""
        
        metadata = FeatureMetadata(
            name=name,
            data_type=data_type,
            description=description,
            source_table=source_table,
            source_column=source_column,
            transformation=transformation,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            tags=tags or [],
            owner=owner
        )
        
        self.feature_metadata[name] = metadata
        self._save_metadata()
        
        self.logger.info("Feature registered", feature_name=name)
    
    def validate_features(self, features: pd.DataFrame) -> bool:
        """Validate feature data."""
        
        try:
            # Check for required columns
            if 'entity_id' not in features.columns:
                self.logger.error("Missing entity_id column")
                return False
            
            # Check for null entity IDs
            if features['entity_id'].isnull().any():
                self.logger.error("Null entity IDs found")
                return False
            
            # Check data types and ranges
            for column in features.columns:
                if column in ['entity_id', 'feature_timestamp', 'feature_group']:
                    continue
                
                metadata = self.feature_metadata.get(column)
                if metadata:
                    # Validate data type
                    expected_type = metadata.data_type
                    if expected_type == 'numeric' and not pd.api.types.is_numeric_dtype(features[column]):
                        self.logger.warning(f"Feature {column} expected numeric but got {features[column].dtype}")
                    
                    # Check quality threshold
                    completeness = 1 - (features[column].isnull().sum() / len(features))
                    if completeness < metadata.quality_threshold:
                        self.logger.warning(
                            f"Feature {column} quality below threshold",
                            completeness=completeness,
                            threshold=metadata.quality_threshold
                        )
            
            return True
            
        except Exception as e:
            self.logger.error("Feature validation failed", error=str(e))
            return False
    
    def detect_drift(
        self,
        reference_data: pd.DataFrame,
        current_data: pd.DataFrame,
        feature_names: Optional[List[str]] = None
    ) -> Dict[str, FeatureDriftReport]:
        """Detect feature drift between reference and current data."""
        
        if feature_names is None:
            # Use common columns (excluding metadata columns)
            feature_names = [
                col for col in reference_data.columns 
                if col in current_data.columns and 
                col not in ['entity_id', 'feature_timestamp', 'feature_group']
            ]
        
        drift_reports = {}
        
        for feature_name in feature_names:
            if feature_name not in reference_data.columns or feature_name not in current_data.columns:
                continue
            
            try:
                drift_report = self._detect_feature_drift(
                    reference_data[feature_name],
                    current_data[feature_name],
                    feature_name
                )
                drift_reports[feature_name] = drift_report
                
            except Exception as e:
                self.logger.error(
                    "Drift detection failed",
                    feature_name=feature_name,
                    error=str(e)
                )
        
        return drift_reports
    
    def _detect_feature_drift(
        self,
        reference_series: pd.Series,
        current_series: pd.Series,
        feature_name: str
    ) -> FeatureDriftReport:
        """Detect drift for a single feature."""
        
        # Get metadata for threshold
        metadata = self.feature_metadata.get(feature_name)
        threshold = metadata.drift_threshold if metadata else 0.1
        
        # Remove null values
        ref_clean = reference_series.dropna()
        cur_clean = current_series.dropna()
        
        if len(ref_clean) == 0 or len(cur_clean) == 0:
            return FeatureDriftReport(
                feature_name=feature_name,
                drift_score=1.0,
                drift_detected=True,
                reference_period=(datetime.now() - timedelta(days=30), datetime.now() - timedelta(days=15)),
                current_period=(datetime.now() - timedelta(days=15), datetime.now()),
                statistical_test="insufficient_data",
                p_value=0.0,
                threshold=threshold,
                recommendations=["Insufficient data for drift detection"]
            )
        
        # Choose appropriate statistical test
        if pd.api.types.is_numeric_dtype(ref_clean):
            # Kolmogorov-Smirnov test for numeric features
            statistic, p_value = stats.ks_2samp(ref_clean, cur_clean)
            test_name = "kolmogorov_smirnov"
        else:
            # Chi-square test for categorical features
            ref_counts = ref_clean.value_counts()
            cur_counts = cur_clean.value_counts()
            
            # Align categories
            all_categories = set(ref_counts.index) | set(cur_counts.index)
            ref_aligned = [ref_counts.get(cat, 0) for cat in all_categories]
            cur_aligned = [cur_counts.get(cat, 0) for cat in all_categories]
            
            if sum(ref_aligned) > 0 and sum(cur_aligned) > 0:
                statistic, p_value = stats.chisquare(cur_aligned, ref_aligned)
                test_name = "chi_square"
            else:
                statistic, p_value = 1.0, 0.0
                test_name = "insufficient_data"
        
        # Determine if drift is detected
        drift_detected = p_value < threshold
        
        # Generate recommendations
        recommendations = []
        if drift_detected:
            recommendations.append("Feature drift detected - consider retraining model")
            recommendations.append("Investigate data source changes")
            if pd.api.types.is_numeric_dtype(ref_clean):
                recommendations.append("Check for distribution shifts in upstream data")
            else:
                recommendations.append("Check for new categories or changed frequencies")
        
        return FeatureDriftReport(
            feature_name=feature_name,
            drift_score=statistic,
            drift_detected=drift_detected,
            reference_period=(datetime.now() - timedelta(days=30), datetime.now() - timedelta(days=15)),
            current_period=(datetime.now() - timedelta(days=15), datetime.now()),
            statistical_test=test_name,
            p_value=p_value,
            threshold=threshold,
            recommendations=recommendations
        )
