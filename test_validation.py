#!/usr/bin/env python3
"""
Simple validation tests for Sensei AI Suite.

Tests basic functionality without complex dependencies.
"""

import pytest
import pandas as pd
import numpy as np
from pathlib import Path
import json
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_project_structure():
    """Test that project has correct structure."""
    required_dirs = [
        'src/sensei',
        'src/sensei/api',
        'src/sensei/models',
        'src/sensei/data',
        'src/sensei/utils',
        'config',
        'models',
        'docs',
        'credentials'
    ]
    
    for dir_path in required_dirs:
        assert Path(dir_path).exists(), f"Missing directory: {dir_path}"


def test_required_files():
    """Test that required files exist."""
    required_files = [
        'README.md',
        'requirements.txt',
        'Dockerfile',
        'train_simple.py',
        'config/settings.py'
    ]
    
    for file_path in required_files:
        assert Path(file_path).exists(), f"Missing file: {file_path}"


def test_config_import():
    """Test that configuration can be imported."""
    try:
        from config.settings import settings
        assert settings is not None
        assert hasattr(settings, 'environment')
        assert hasattr(settings, 'database')
    except ImportError as e:
        pytest.fail(f"Cannot import settings: {e}")


def test_model_registry():
    """Test model registry functionality."""
    registry_path = Path("models/registry.json")
    
    if registry_path.exists():
        with open(registry_path, 'r') as f:
            registry = json.load(f)
        
        # Check that validation results exist
        assert 'conversion' in registry or 'channel' in registry
        
        # Check structure of entries
        for model_name in ['conversion', 'channel']:
            if model_name in registry:
                entries = registry[model_name]
                assert isinstance(entries, list)
                
                if entries:
                    entry = entries[0]
                    assert 'version' in entry
                    assert 'metrics' in entry
                    assert 'created_at' in entry


def test_simple_training_models():
    """Test that simple training models work."""
    from train_simple import SimpleConversionModel, SimpleChannelModel
    
    # Test conversion model
    conv_model = SimpleConversionModel()
    assert not conv_model.is_trained
    
    # Create test data
    test_data = pd.DataFrame({'id': range(100)})
    X, y = conv_model.prepare_features(test_data)
    
    assert isinstance(X, pd.DataFrame)
    assert isinstance(y, pd.Series)
    assert len(X) == len(y) == 100
    
    # Test channel model
    channel_model = SimpleChannelModel()
    assert not channel_model.is_trained
    
    X, y = channel_model.prepare_features(test_data)
    assert isinstance(X, pd.DataFrame)
    assert isinstance(y, pd.Series)
    assert len(X) == len(y) == 100


def test_data_validation():
    """Test basic data validation functions."""
    # Test synthetic data creation
    from train_simple import create_synthetic_data
    
    data = create_synthetic_data(50)
    assert isinstance(data, pd.DataFrame)
    assert len(data) == 50
    assert 'id_prospect' in data.columns
    assert 'email' in data.columns
    assert 'nom' in data.columns


def test_model_training_pipeline():
    """Test complete training pipeline with small data."""
    from train_simple import SimpleConversionModel, create_synthetic_data
    
    # Create small dataset
    data = create_synthetic_data(100)
    
    # Test conversion model pipeline
    model = SimpleConversionModel()
    X, y = model.prepare_features(data)
    metrics = model.train(X, y)
    
    # Validate metrics
    assert isinstance(metrics, dict)
    assert 'train_auc' in metrics
    assert 'val_auc' in metrics
    assert 'feature_count' in metrics
    
    # Check that model can predict
    predictions = model.predict(X)
    assert len(predictions) == len(X)
    assert all(0 <= p <= 1 for p in predictions)


def test_requirements_compatibility():
    """Test that minimal requirements are compatible."""
    try:
        import numpy
        import pandas
        import sklearn
        import xgboost
        import catboost
        import fastapi
        import uvicorn
        import pydantic
        
        # Check versions are reasonable
        assert numpy.__version__.startswith('1.')
        assert pandas.__version__.startswith('2.')
        
    except ImportError as e:
        pytest.fail(f"Missing required dependency: {e}")


def test_environment_variables():
    """Test environment variable handling."""
    from config.settings import settings
    
    # Test that settings handle missing env vars gracefully
    assert settings.database.project_id is not None
    assert settings.database.dataset_id is not None
    assert settings.api.port is not None


def test_logging_functionality():
    """Test that logging works."""
    from train_simple import log_info, log_error
    
    # These should not raise exceptions
    log_info("Test message", test_param="value")
    log_error("Test error", error_code=123)


def test_model_metrics_validation():
    """Test that model metrics are realistic."""
    registry_path = Path("models/registry.json")
    
    if registry_path.exists():
        with open(registry_path, 'r') as f:
            registry = json.load(f)
        
        # Check conversion model metrics
        if 'conversion' in registry:
            for entry in registry['conversion']:
                metrics = entry.get('metrics', {})
                if 'val_auc' in metrics:
                    auc = metrics['val_auc']
                    # AUC should be between 0 and 1
                    assert 0 <= auc <= 1
                    # For validation data, should be reasonably close to 0.5 (random)
                    if entry.get('type') == 'validation':
                        assert 0.3 <= auc <= 0.7, f"Validation AUC too extreme: {auc}"
        
        # Check channel model metrics
        if 'channel' in registry:
            for entry in registry['channel']:
                metrics = entry.get('metrics', {})
                if 'val_accuracy' in metrics:
                    acc = metrics['val_accuracy']
                    # Accuracy should be between 0 and 1
                    assert 0 <= acc <= 1


def test_docker_file_validity():
    """Test that Dockerfile has correct structure."""
    dockerfile_path = Path("Dockerfile")
    assert dockerfile_path.exists()
    
    with open(dockerfile_path, 'r') as f:
        content = f.read()
    
    # Check for essential Docker commands
    assert 'FROM python:3.11' in content
    assert 'COPY requirements.txt' in content
    assert 'RUN pip install' in content
    assert 'EXPOSE 8000' in content
    assert 'CMD' in content or 'ENTRYPOINT' in content


if __name__ == "__main__":
    # Run tests directly
    import subprocess
    import sys
    
    print("Running validation tests...")
    
    # Run pytest
    result = subprocess.run([
        sys.executable, '-m', 'pytest', __file__, '-v'
    ], capture_output=True, text=True)
    
    print(result.stdout)
    if result.stderr:
        print("STDERR:", result.stderr)
    
    sys.exit(result.returncode)
