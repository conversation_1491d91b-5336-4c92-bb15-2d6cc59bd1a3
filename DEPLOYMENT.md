# 🚀 Sensei AI Suite v2.0 - Deployment Guide

## 📋 Table of Contents
- [Prerequisites](#prerequisites)
- [Environment Setup](#environment-setup)
- [Local Development](#local-development)
- [Production Deployment](#production-deployment)
- [Monitoring & Maintenance](#monitoring--maintenance)
- [Troubleshooting](#troubleshooting)

## 🔧 Prerequisites

### System Requirements
- **Python**: 3.11+
- **Memory**: 8GB+ RAM (16GB+ recommended)
- **Storage**: 50GB+ available space
- **CPU**: 4+ cores (8+ recommended)
- **Network**: Stable internet connection

### Required Services
- **Google Cloud Platform**: BigQuery, Secret Manager, Storage
- **Redis**: For caching (optional but recommended)
- **Docker**: For containerization
- **Kubernetes**: For production deployment (optional)

### Development Tools
```bash
# Install required tools
pip install --upgrade pip
pip install poetry  # For dependency management
pip install pre-commit  # For code quality
pip install docker-compose  # For local services
```

## 🌍 Environment Setup

### 1. **Clone Repository**
```bash
git clone https://github.com/your-org/sensei-ai-suite.git
cd sensei-ai-suite
```

### 2. **Environment Variables**
Create `.env` file:
```bash
# Environment Configuration
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Database Configuration
GCP_PROJECT_ID=datalake-sensei
DB_DATASET_ID=sensei_data
DB_LOCATION=europe-west1

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1
API_RELOAD=true

# Security Configuration
SECURITY_SECRET_KEY=your-secret-key-here
SECURITY_PII_HASH_SALT=your-salt-here

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379

# Monitoring Configuration
MONITORING_ENABLE_METRICS=true
MONITORING_METRICS_PORT=9090
```

### 3. **Google Cloud Setup**
```bash
# Install Google Cloud SDK
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Authenticate
gcloud auth login
gcloud config set project datalake-sensei

# Create service account
gcloud iam service-accounts create sensei-ai-service \
    --display-name="Sensei AI Service Account"

# Grant permissions
gcloud projects add-iam-policy-binding datalake-sensei \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/bigquery.dataViewer"

gcloud projects add-iam-policy-binding datalake-sensei \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/secretmanager.secretAccessor"

# Download service account key
gcloud iam service-accounts keys create credentials/sensei-ai-service-account.json \
    --iam-account=<EMAIL>

# Set environment variable
export GOOGLE_APPLICATION_CREDENTIALS=credentials/sensei-ai-service-account.json
```

## 💻 Local Development

### 1. **Install Dependencies**
```bash
# Install v2.0 requirements
pip install -r requirements-v2.txt

# Or using poetry (recommended)
poetry install
poetry shell
```

### 2. **Start Local Services**
```bash
# Start Redis (optional)
docker run -d --name redis -p 6379:6379 redis:alpine

# Or using docker-compose
docker-compose up -d redis
```

### 3. **Validate Installation**
```bash
# Run validation suite
python validate_v2.py --quick

# Expected output:
# ✅ SYSTEM validation PASSED
# ✅ CONFIGURATION validation PASSED
# ✅ MODELS validation PASSED
# ✅ API validation PASSED
# 🎉 ALL VALIDATIONS PASSED - Sensei AI Suite v2.0 is ready!
```

### 4. **Train Models**
```bash
# Train all models with synthetic data
python train_v2.py --models conversion_v2 channel_v2 nlp_v2 --max-samples 1000

# Train specific model
python train_v2.py --models conversion_v2 --max-samples 5000

# Train with hyperparameter optimization
python train_v2.py --models conversion_v2 --max-samples 10000
```

### 5. **Start API Server**
```bash
# Development server with auto-reload
uvicorn src.sensei.api.v2.main:app --host 0.0.0.0 --port 8000 --reload

# Or using the script
python -m src.sensei.api.v2.main
```

### 6. **Test API**
```bash
# Health check
curl http://localhost:8000/health

# API documentation
open http://localhost:8000/docs

# Test prediction
curl -X POST "http://localhost:8000/predict/conversion" \
     -H "Content-Type: application/json" \
     -d '{
       "prospect_id": "test_123",
       "company_size": "medium",
       "nb_interactions": 5,
       "engagement_score": 0.7
     }'
```

### 7. **Run Tests**
```bash
# Run all tests
pytest tests/test_v2_integration.py -v

# Run specific test category
pytest tests/test_v2_integration.py::TestAPIIntegration -v

# Run with coverage
pytest tests/test_v2_integration.py --cov=src/sensei --cov-report=html
```

## 🏭 Production Deployment

### 1. **Docker Deployment**

#### Build Container
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements-v2.txt .
RUN pip install --no-cache-dir -r requirements-v2.txt

# Copy application code
COPY src/ src/
COPY credentials/ credentials/

# Set environment variables
ENV PYTHONPATH=/app/src
ENV GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/sensei-ai-service-account.json

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start application
CMD ["uvicorn", "src.sensei.api.v2.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Build and Run
```bash
# Build image
docker build -t sensei-ai-v2:latest .

# Run container
docker run -d \
    --name sensei-ai-v2 \
    -p 8000:8000 \
    -e ENVIRONMENT=production \
    -e GCP_PROJECT_ID=datalake-sensei \
    -v $(pwd)/credentials:/app/credentials:ro \
    sensei-ai-v2:latest
```

### 2. **Kubernetes Deployment**

#### Deployment Configuration
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sensei-ai-v2
  labels:
    app: sensei-ai-v2
spec:
  replicas: 3
  selector:
    matchLabels:
      app: sensei-ai-v2
  template:
    metadata:
      labels:
        app: sensei-ai-v2
    spec:
      containers:
      - name: sensei-ai-v2
        image: sensei-ai-v2:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: GCP_PROJECT_ID
          value: "datalake-sensei"
        - name: API_WORKERS
          value: "4"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: sensei-ai-v2-service
spec:
  selector:
    app: sensei-ai-v2
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
```

#### Deploy to Kubernetes
```bash
# Apply deployment
kubectl apply -f k8s/deployment.yaml

# Check status
kubectl get pods -l app=sensei-ai-v2
kubectl get services

# Scale deployment
kubectl scale deployment sensei-ai-v2 --replicas=5

# Update deployment
kubectl set image deployment/sensei-ai-v2 sensei-ai-v2=sensei-ai-v2:v2.1.0
```

### 3. **Auto-scaling Configuration**
```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: sensei-ai-v2-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: sensei-ai-v2
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 4. **Load Balancer Configuration**
```yaml
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: sensei-ai-v2-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "1000"
spec:
  tls:
  - hosts:
    - api.sensei-ai.com
    secretName: sensei-ai-tls
  rules:
  - host: api.sensei-ai.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: sensei-ai-v2-service
            port:
              number: 80
```

## 📊 Monitoring & Maintenance

### 1. **Health Monitoring**
```bash
# Check application health
curl https://api.sensei-ai.com/health

# Check metrics
curl https://api.sensei-ai.com/metrics

# Monitor logs
kubectl logs -f deployment/sensei-ai-v2
```

### 2. **Performance Monitoring**
```bash
# Monitor resource usage
kubectl top pods -l app=sensei-ai-v2

# Check HPA status
kubectl get hpa

# Monitor API performance
curl -w "@curl-format.txt" -s -o /dev/null https://api.sensei-ai.com/health
```

### 3. **Model Monitoring**
```bash
# Check model status
curl https://api.sensei-ai.com/models

# Monitor prediction accuracy
python scripts/monitor_model_performance.py

# Check for data drift
python scripts/check_data_drift.py
```

### 4. **Backup & Recovery**
```bash
# Backup models
gsutil -m cp -r models/ gs://sensei-ai-backups/models/$(date +%Y%m%d)/

# Backup configuration
kubectl get configmap sensei-ai-config -o yaml > backup/config-$(date +%Y%m%d).yaml

# Restore from backup
gsutil -m cp -r gs://sensei-ai-backups/models/20240115/ models/
```

## 🔧 Troubleshooting

### Common Issues

#### 1. **API Not Starting**
```bash
# Check logs
kubectl logs deployment/sensei-ai-v2

# Common causes:
# - Missing environment variables
# - Invalid credentials
# - Port conflicts
# - Resource limits

# Solutions:
kubectl describe pod <pod-name>
kubectl get events --sort-by=.metadata.creationTimestamp
```

#### 2. **High Memory Usage**
```bash
# Check memory usage
kubectl top pods

# Enable memory optimization
export ENABLE_MEMORY_OPTIMIZATION=true

# Restart with more memory
kubectl patch deployment sensei-ai-v2 -p '{"spec":{"template":{"spec":{"containers":[{"name":"sensei-ai-v2","resources":{"limits":{"memory":"8Gi"}}}]}}}}'
```

#### 3. **Slow API Response**
```bash
# Check performance metrics
curl https://api.sensei-ai.com/metrics

# Enable caching
export REDIS_URL=redis://redis-service:6379

# Scale up replicas
kubectl scale deployment sensei-ai-v2 --replicas=10
```

#### 4. **Model Training Failures**
```bash
# Check training logs
python train_v2.py --debug

# Common causes:
# - Insufficient data
# - Memory limits
# - Invalid hyperparameters

# Solutions:
# - Reduce batch size
# - Increase memory limits
# - Use synthetic data for testing
```

### Performance Tuning

#### 1. **API Optimization**
```python
# Increase worker processes
API_WORKERS=4

# Enable async processing
ENABLE_ASYNC=true

# Optimize database connections
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
```

#### 2. **Model Optimization**
```python
# Enable model caching
MODEL_CACHE_SIZE=10

# Use optimized models
USE_OPTIMIZED_MODELS=true

# Enable parallel prediction
ENABLE_PARALLEL_PREDICTION=true
```

#### 3. **Database Optimization**
```sql
-- Optimize BigQuery queries
-- Use partitioned tables
-- Implement query caching
-- Use appropriate data types
```

### Maintenance Tasks

#### Weekly Tasks
- [ ] Check system health and performance metrics
- [ ] Review error logs and alerts
- [ ] Update security patches
- [ ] Backup models and configuration

#### Monthly Tasks
- [ ] Review and optimize resource usage
- [ ] Update dependencies
- [ ] Performance testing
- [ ] Security audit

#### Quarterly Tasks
- [ ] Model retraining with new data
- [ ] Architecture review
- [ ] Disaster recovery testing
- [ ] Capacity planning

---

*For additional support, contact the Sensei AI Team or create an issue in the repository.*
