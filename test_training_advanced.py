#!/usr/bin/env python3
"""
Test d'entraînement avancé pour Sensei AI Suite v2.0

Ce script teste l'entraînement avec:
- XGBoost
- CatBoost  
- Ensemble methods
- Model validation
"""

import sys
import os
import time
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, Any

def print_section(title: str):
    """Print a formatted section header."""
    print("\n" + "=" * 60)
    print(f"🧪 {title}")
    print("=" * 60)

def create_synthetic_data(n_samples: int = 2000) -> pd.DataFrame:
    """Create synthetic training data for conversion prediction."""
    
    print(f"📊 Creating {n_samples} synthetic training samples...")
    
    np.random.seed(42)
    
    # Features
    data = {
        # Interaction features
        'nb_interactions': np.random.poisson(5, n_samples) + 1,
        'days_since_last_contact': np.random.exponential(7, n_samples),
        'engagement_score': np.random.beta(2, 3, n_samples),
        
        # Company features
        'company_size': np.random.choice(['small', 'medium', 'large'], n_samples, p=[0.4, 0.4, 0.2]),
        'industry': np.random.choice(['tech', 'finance', 'retail', 'healthcare'], n_samples),
        'revenue_range': np.random.choice(['0-1M', '1-10M', '10M+'], n_samples, p=[0.5, 0.3, 0.2]),
        
        # Contact features
        'contact_method': np.random.choice(['email', 'phone', 'linkedin'], n_samples, p=[0.6, 0.2, 0.2]),
        'time_of_contact': np.random.choice(['morning', 'afternoon', 'evening'], n_samples),
        
        # Behavioral features
        'website_visits': np.random.poisson(3, n_samples),
        'email_opens': np.random.poisson(2, n_samples),
        'document_downloads': np.random.poisson(1, n_samples),
    }
    
    df = pd.DataFrame(data)
    
    # Encode categorical variables
    df['company_size_encoded'] = df['company_size'].map({'small': 0, 'medium': 1, 'large': 2})
    df['industry_encoded'] = df['industry'].map({'tech': 0, 'finance': 1, 'retail': 2, 'healthcare': 3})
    df['revenue_encoded'] = df['revenue_range'].map({'0-1M': 0, '1-10M': 1, '10M+': 2})
    df['contact_method_encoded'] = df['contact_method'].map({'email': 0, 'phone': 1, 'linkedin': 2})
    df['time_encoded'] = df['time_of_contact'].map({'morning': 0, 'afternoon': 1, 'evening': 2})
    
    # Create realistic conversion target
    conversion_logit = (
        0.1 * np.log1p(df['nb_interactions']) +
        0.2 * df['engagement_score'] +
        0.15 * df['company_size_encoded'] +
        0.1 * df['revenue_encoded'] +
        -0.05 * np.log1p(df['days_since_last_contact']) +
        0.05 * np.log1p(df['website_visits']) +
        0.03 * np.log1p(df['email_opens']) +
        0.08 * np.log1p(df['document_downloads']) +
        np.random.normal(0, 0.3, n_samples)
    )
    
    # Convert to probability and binary target
    conversion_prob = 1 / (1 + np.exp(-conversion_logit))
    df['converted'] = np.random.binomial(1, conversion_prob)
    df['conversion_probability'] = conversion_prob
    
    print(f"  ✅ Conversion rate: {df['converted'].mean():.3f}")
    print(f"  ✅ Features created: {len([col for col in df.columns if col.endswith('_encoded')])}")
    
    return df

def test_xgboost_training(df: pd.DataFrame) -> Dict[str, Any]:
    """Test XGBoost model training."""
    
    print_section("XGBoost Model Training")
    
    try:
        import xgboost as xgb
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
        
        # Prepare features
        feature_cols = [col for col in df.columns if col.endswith('_encoded')] + [
            'nb_interactions', 'days_since_last_contact', 'engagement_score',
            'website_visits', 'email_opens', 'document_downloads'
        ]
        
        X = df[feature_cols]
        y = df['converted']
        
        print(f"📊 Training with {len(feature_cols)} features on {len(X)} samples")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
        
        # Train XGBoost
        print("🤖 Training XGBoost model...")
        model = xgb.XGBClassifier(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            random_state=42,
            eval_metric='logloss'
        )
        
        model.fit(X_train, y_train)
        
        # Evaluate
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1]
        
        accuracy = accuracy_score(y_test, y_pred)
        auc = roc_auc_score(y_test, y_pred_proba)
        
        print(f"  ✅ Accuracy: {accuracy:.3f}")
        print(f"  ✅ AUC: {auc:.3f}")
        
        # Feature importance
        feature_importance = dict(zip(feature_cols, model.feature_importances_))
        top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:5]
        print("  📊 Top 5 features:")
        for feat, importance in top_features:
            print(f"    {feat}: {importance:.3f}")
        
        # Save model
        os.makedirs("models", exist_ok=True)
        model_path = "models/xgboost_conversion_model.pkl"
        import joblib
        joblib.dump(model, model_path)
        print(f"  ✅ Model saved to {model_path}")
        
        return {
            "model_type": "XGBoost",
            "accuracy": accuracy,
            "auc": auc,
            "n_features": len(feature_cols),
            "model_path": model_path
        }
        
    except Exception as e:
        print(f"  ❌ XGBoost training failed: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

def test_catboost_training(df: pd.DataFrame) -> Dict[str, Any]:
    """Test CatBoost model training."""
    
    print_section("CatBoost Model Training")
    
    try:
        from catboost import CatBoostClassifier
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score, roc_auc_score
        
        # Prepare features (CatBoost can handle categorical features directly)
        feature_cols = [
            'nb_interactions', 'days_since_last_contact', 'engagement_score',
            'website_visits', 'email_opens', 'document_downloads',
            'company_size', 'industry', 'revenue_range', 'contact_method', 'time_of_contact'
        ]
        
        categorical_features = ['company_size', 'industry', 'revenue_range', 'contact_method', 'time_of_contact']
        
        X = df[feature_cols]
        y = df['converted']
        
        print(f"📊 Training with {len(feature_cols)} features ({len(categorical_features)} categorical)")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
        
        # Train CatBoost
        print("🤖 Training CatBoost model...")
        model = CatBoostClassifier(
            iterations=100,
            depth=6,
            learning_rate=0.1,
            random_seed=42,
            cat_features=categorical_features,
            verbose=False
        )
        
        model.fit(X_train, y_train)
        
        # Evaluate
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1]
        
        accuracy = accuracy_score(y_test, y_pred)
        auc = roc_auc_score(y_test, y_pred_proba)
        
        print(f"  ✅ Accuracy: {accuracy:.3f}")
        print(f"  ✅ AUC: {auc:.3f}")
        
        # Feature importance
        feature_importance = dict(zip(feature_cols, model.feature_importances_))
        top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:5]
        print("  📊 Top 5 features:")
        for feat, importance in top_features:
            print(f"    {feat}: {importance:.3f}")
        
        # Save model
        model_path = "models/catboost_conversion_model.pkl"
        import joblib
        joblib.dump(model, model_path)
        print(f"  ✅ Model saved to {model_path}")
        
        return {
            "model_type": "CatBoost",
            "accuracy": accuracy,
            "auc": auc,
            "n_features": len(feature_cols),
            "model_path": model_path
        }
        
    except Exception as e:
        print(f"  ❌ CatBoost training failed: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

def test_ensemble_prediction(df: pd.DataFrame, model_results: list) -> Dict[str, Any]:
    """Test ensemble prediction combining multiple models."""
    
    print_section("Ensemble Model Testing")
    
    try:
        import joblib
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score, roc_auc_score
        
        # Load trained models
        models = []
        for result in model_results:
            if "model_path" in result and os.path.exists(result["model_path"]):
                model = joblib.load(result["model_path"])
                models.append((result["model_type"], model))
                print(f"  ✅ Loaded {result['model_type']} model")
        
        if len(models) < 2:
            print("  ⚠️  Need at least 2 models for ensemble")
            return {"error": "Insufficient models"}
        
        # Prepare test data
        feature_cols_xgb = [col for col in df.columns if col.endswith('_encoded')] + [
            'nb_interactions', 'days_since_last_contact', 'engagement_score',
            'website_visits', 'email_opens', 'document_downloads'
        ]
        
        feature_cols_cat = [
            'nb_interactions', 'days_since_last_contact', 'engagement_score',
            'website_visits', 'email_opens', 'document_downloads',
            'company_size', 'industry', 'revenue_range', 'contact_method', 'time_of_contact'
        ]
        
        _, X_test_xgb, _, y_test = train_test_split(
            df[feature_cols_xgb], df['converted'], test_size=0.2, random_state=42, stratify=df['converted']
        )
        _, X_test_cat, _, _ = train_test_split(
            df[feature_cols_cat], df['converted'], test_size=0.2, random_state=42, stratify=df['converted']
        )
        
        # Get predictions from each model
        predictions = []
        for model_name, model in models:
            if model_name == "XGBoost":
                pred_proba = model.predict_proba(X_test_xgb)[:, 1]
            else:  # CatBoost
                pred_proba = model.predict_proba(X_test_cat)[:, 1]
            predictions.append(pred_proba)
            print(f"  ✅ {model_name} predictions generated")
        
        # Ensemble prediction (simple average)
        ensemble_pred_proba = np.mean(predictions, axis=0)
        ensemble_pred = (ensemble_pred_proba > 0.5).astype(int)
        
        # Evaluate ensemble
        accuracy = accuracy_score(y_test, ensemble_pred)
        auc = roc_auc_score(y_test, ensemble_pred_proba)
        
        print(f"  ✅ Ensemble Accuracy: {accuracy:.3f}")
        print(f"  ✅ Ensemble AUC: {auc:.3f}")
        
        # Compare with individual models
        print("  📊 Model comparison:")
        for i, (model_name, _) in enumerate(models):
            individual_pred = (predictions[i] > 0.5).astype(int)
            individual_acc = accuracy_score(y_test, individual_pred)
            individual_auc = roc_auc_score(y_test, predictions[i])
            print(f"    {model_name}: Acc={individual_acc:.3f}, AUC={individual_auc:.3f}")
        
        return {
            "model_type": "Ensemble",
            "accuracy": accuracy,
            "auc": auc,
            "n_models": len(models),
            "models_used": [name for name, _ in models]
        }
        
    except Exception as e:
        print(f"  ❌ Ensemble testing failed: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

def main():
    """Main training test function."""
    
    print_section("SENSEI AI SUITE V2.0 - TEST ENTRAÎNEMENT AVANCÉ")
    
    start_time = time.time()
    
    # Create synthetic data
    df = create_synthetic_data(2000)
    
    # Train models
    model_results = []
    
    # Test XGBoost
    xgb_result = test_xgboost_training(df)
    model_results.append(xgb_result)
    
    # Test CatBoost
    cat_result = test_catboost_training(df)
    model_results.append(cat_result)
    
    # Test Ensemble
    ensemble_result = test_ensemble_prediction(df, model_results)
    
    # Summary
    print_section("RÉSUMÉ DES RÉSULTATS")
    
    all_results = model_results + [ensemble_result]
    
    for result in all_results:
        if "error" not in result:
            print(f"  🤖 {result['model_type']}:")
            print(f"    Accuracy: {result['accuracy']:.3f}")
            print(f"    AUC: {result['auc']:.3f}")
            if 'n_features' in result:
                print(f"    Features: {result['n_features']}")
        else:
            print(f"  ❌ {result.get('model_type', 'Unknown')}: {result['error']}")
    
    elapsed_time = time.time() - start_time
    print(f"\n⏱️  DURÉE TOTALE: {elapsed_time:.2f} secondes")
    
    # Check if all models trained successfully
    successful_models = [r for r in all_results if "error" not in r]
    
    if len(successful_models) >= 2:
        print("\n🎉 ENTRAÎNEMENT RÉUSSI!")
        print("🚀 Modèles ML v2.0 prêts pour la production!")
        return True
    else:
        print("\n⚠️  Certains modèles ont échoué")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
