# Sensei AI Suite v2.0 - Git Ignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Environment variables
.env
.env.local
.env.production

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# Models and data
models/*.pkl
models/*.joblib
models/*.h5
models/*.onnx
data/*.csv
data/*.parquet
data/*.json
logs/*.log

# Credentials
credentials/*.json
!credentials/*.json.example

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Monitoring
monitoring/prometheus/data/
monitoring/grafana/data/

# Testing
.coverage
.pytest_cache/
htmlcov/
.tox/

# Build artifacts
*.tar.gz
*.zip
build-report-*.json

# Temporary files
*.tmp
*.temp
.cache/
