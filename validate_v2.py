#!/usr/bin/env python3
"""
Comprehensive validation script for Sensei AI Suite v2.0.

Validates:
- System requirements and dependencies
- Configuration and environment
- Model training and prediction
- API functionality
- Performance benchmarks
- Data pipeline integrity
"""

import asyncio
import sys
import time
import traceback
from pathlib import Path
from typing import Dict, List, Tuple

import pandas as pd
import numpy as np
import requests
from fastapi.testclient import TestClient

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from sensei.core.config import get_settings, configure_logging
from sensei.core.logging import get_logger
from sensei.core.exceptions import SenseiException
from sensei.models.conversion_v2 import ConversionModelV2
from sensei.models.channel_v2 import ChannelModelV2
from sensei.models.nlp_v2 import NLPModelV2
from sensei.data.manager import BigQueryDataSource
from sensei.features.store import AdvancedFeatureStore


class ValidationSuite:
    """Comprehensive validation suite for Sensei AI Suite v2.0."""
    
    def __init__(self):
        """Initialize validation suite."""
        configure_logging(level="INFO", json_logs=False)
        self.logger = get_logger(__name__)
        self.settings = get_settings()
        self.results: Dict[str, Dict] = {}
        
        self.logger.info("Sensei AI Suite v2.0 Validation Suite")
        self.logger.info("="*60)
    
    def run_validation(self, categories: List[str] = None) -> bool:
        """Run comprehensive validation."""
        
        if categories is None:
            categories = [
                "system",
                "configuration", 
                "models",
                "data_pipeline",
                "api",
                "performance"
            ]
        
        overall_success = True
        
        for category in categories:
            self.logger.info(f"\n🔍 Validating {category.upper()}...")
            
            try:
                if category == "system":
                    success = self._validate_system_requirements()
                elif category == "configuration":
                    success = self._validate_configuration()
                elif category == "models":
                    success = asyncio.run(self._validate_models())
                elif category == "data_pipeline":
                    success = self._validate_data_pipeline()
                elif category == "api":
                    success = asyncio.run(self._validate_api())
                elif category == "performance":
                    success = self._validate_performance()
                else:
                    self.logger.warning(f"Unknown validation category: {category}")
                    continue
                
                self.results[category] = {
                    "success": success,
                    "timestamp": time.time()
                }
                
                if success:
                    self.logger.info(f"✅ {category.upper()} validation PASSED")
                else:
                    self.logger.error(f"❌ {category.upper()} validation FAILED")
                    overall_success = False
                    
            except Exception as e:
                self.logger.error(f"❌ {category.upper()} validation ERROR: {e}")
                self.results[category] = {
                    "success": False,
                    "error": str(e),
                    "timestamp": time.time()
                }
                overall_success = False
        
        self._print_summary()
        return overall_success
    
    def _validate_system_requirements(self) -> bool:
        """Validate system requirements and dependencies."""
        
        checks = []
        
        # Python version
        python_version = sys.version_info
        if python_version >= (3, 11):
            checks.append(("Python 3.11+", True, f"✓ Python {python_version.major}.{python_version.minor}"))
        else:
            checks.append(("Python 3.11+", False, f"✗ Python {python_version.major}.{python_version.minor} (requires 3.11+)"))
        
        # Required packages
        required_packages = [
            "pandas", "numpy", "scikit-learn", "fastapi", "uvicorn",
            "pydantic", "structlog", "optuna", "xgboost", "lightgbm", "catboost"
        ]
        
        for package in required_packages:
            try:
                __import__(package)
                checks.append((f"Package {package}", True, f"✓ {package} installed"))
            except ImportError:
                checks.append((f"Package {package}", False, f"✗ {package} missing"))
        
        # Optional packages
        optional_packages = [
            "transformers", "sentence_transformers", "torch", "umap", "hdbscan"
        ]
        
        for package in optional_packages:
            try:
                __import__(package)
                checks.append((f"Optional {package}", True, f"✓ {package} available"))
            except ImportError:
                checks.append((f"Optional {package}", False, f"⚠ {package} missing (optional)"))
        
        # Directory structure
        required_dirs = [
            self.settings.models_dir,
            self.settings.data_dir,
            self.settings.logs_dir
        ]
        
        for dir_path in required_dirs:
            if dir_path.exists():
                checks.append((f"Directory {dir_path.name}", True, f"✓ {dir_path} exists"))
            else:
                dir_path.mkdir(parents=True, exist_ok=True)
                checks.append((f"Directory {dir_path.name}", True, f"✓ {dir_path} created"))
        
        # Print results
        for name, success, message in checks:
            if success:
                self.logger.info(f"  {message}")
            else:
                self.logger.error(f"  {message}")
        
        return all(success for _, success, _ in checks if not _.startswith("Optional"))
    
    def _validate_configuration(self) -> bool:
        """Validate configuration and environment."""
        
        checks = []
        
        # Settings validation
        try:
            settings = get_settings()
            checks.append(("Settings loading", True, "✓ Settings loaded successfully"))
            
            # Check critical settings
            if settings.database.project_id:
                checks.append(("Database project ID", True, f"✓ Project: {settings.database.project_id}"))
            else:
                checks.append(("Database project ID", False, "✗ No project ID configured"))
            
            if settings.api.port:
                checks.append(("API port", True, f"✓ Port: {settings.api.port}"))
            else:
                checks.append(("API port", False, "✗ No API port configured"))
            
        except Exception as e:
            checks.append(("Settings loading", False, f"✗ Settings error: {e}"))
        
        # Environment variables
        env_vars = ["ENVIRONMENT", "GCP_PROJECT_ID"]
        for var in env_vars:
            import os
            if os.getenv(var):
                checks.append((f"Environment {var}", True, f"✓ {var} set"))
            else:
                checks.append((f"Environment {var}", False, f"⚠ {var} not set"))
        
        # Print results
        for name, success, message in checks:
            if success:
                self.logger.info(f"  {message}")
            else:
                self.logger.warning(f"  {message}")
        
        return all(success for _, success, _ in checks)
    
    async def _validate_models(self) -> bool:
        """Validate model training and prediction."""
        
        model_tests = [
            ("ConversionModelV2", ConversionModelV2, self._test_conversion_model),
            ("ChannelModelV2", ChannelModelV2, self._test_channel_model),
            ("NLPModelV2", NLPModelV2, self._test_nlp_model)
        ]
        
        all_passed = True
        
        for model_name, model_class, test_func in model_tests:
            try:
                self.logger.info(f"  Testing {model_name}...")
                success = await test_func(model_class)
                
                if success:
                    self.logger.info(f"    ✓ {model_name} validation passed")
                else:
                    self.logger.error(f"    ✗ {model_name} validation failed")
                    all_passed = False
                    
            except Exception as e:
                self.logger.error(f"    ✗ {model_name} error: {e}")
                all_passed = False
        
        return all_passed
    
    async def _test_conversion_model(self, model_class) -> bool:
        """Test conversion model."""
        try:
            # Create synthetic data
            np.random.seed(42)
            n_samples = 100
            
            X = pd.DataFrame({
                'company_size': np.random.choice(['small', 'medium', 'large'], n_samples),
                'nb_interactions': np.random.poisson(5, n_samples),
                'engagement_score': np.random.beta(2, 5, n_samples),
                'days_in_pipeline': np.random.exponential(30, n_samples).astype(int)
            })
            y = pd.Series(np.random.binomial(1, 0.15, n_samples))
            
            # Test model
            model = model_class()
            metrics = model.train(X, y, optimize_hyperparams=False)
            
            # Test prediction
            predictions = model.predict(X.head(10))
            probabilities = model.predict_proba(X.head(10))
            
            # Validate results
            assert len(predictions) == 10
            assert len(probabilities) == 10
            assert all(p in [0, 1] for p in predictions)
            assert metrics is not None
            
            return True
            
        except Exception as e:
            self.logger.error(f"Conversion model test failed: {e}")
            return False
    
    async def _test_channel_model(self, model_class) -> bool:
        """Test channel model."""
        try:
            # Create synthetic data
            np.random.seed(42)
            n_samples = 100
            
            X = pd.DataFrame({
                'has_email': np.random.choice([True, False], n_samples, p=[0.9, 0.1]),
                'has_phone': np.random.choice([True, False], n_samples, p=[0.8, 0.2]),
                'email_response_rate': np.random.beta(2, 8, n_samples),
                'urgency_level': np.random.beta(2, 3, n_samples)
            })
            
            # Test model (without training for speed)
            model = model_class(use_deep_learning=False)
            
            # Create dummy training to mark as trained
            model.is_fitted = True
            model.feature_names = X.columns.tolist()
            
            # Test basic functionality
            assert model.model_name == "channel_v2"
            assert hasattr(model, 'CHANNELS')
            assert hasattr(model, 'TIMINGS')
            
            return True
            
        except Exception as e:
            self.logger.error(f"Channel model test failed: {e}")
            return False
    
    async def _test_nlp_model(self, model_class) -> bool:
        """Test NLP model."""
        try:
            # Create synthetic data
            sample_texts = [
                "Bonjour, je suis intéressé par votre solution.",
                "Pouvez-vous me donner plus d'informations ?",
                "C'est urgent, j'ai besoin d'une réponse.",
                "Merci pour votre présentation.",
                "Je ne suis pas sûr de nos besoins."
            ]
            
            X = pd.DataFrame({
                'content': sample_texts * 20,  # 100 samples
                'language': ['fr'] * 100
            })
            
            # Test model
            model = model_class()
            
            # Test basic functionality
            assert model.model_name == "nlp_v2"
            assert hasattr(model, 'enable_sentiment')
            assert hasattr(model, 'enable_intent')
            
            # Test preprocessing
            processed = model._preprocess_text(sample_texts)
            assert len(processed) == len(sample_texts)
            
            return True
            
        except Exception as e:
            self.logger.error(f"NLP model test failed: {e}")
            return False
    
    def _validate_data_pipeline(self) -> bool:
        """Validate data pipeline components."""
        
        checks = []
        
        try:
            # Test BigQuery data source
            data_source = BigQueryDataSource()
            checks.append(("BigQuery client", True, "✓ BigQuery client created"))
            
            # Test feature store
            feature_store = AdvancedFeatureStore(data_source)
            checks.append(("Feature store", True, "✓ Feature store initialized"))
            
            # Test query validation
            valid_query = "SELECT * FROM test_table LIMIT 10"
            invalid_query = "DROP TABLE test_table"
            
            if data_source.validate_query(valid_query):
                checks.append(("Query validation (valid)", True, "✓ Valid query accepted"))
            else:
                checks.append(("Query validation (valid)", False, "✗ Valid query rejected"))
            
            if not data_source.validate_query(invalid_query):
                checks.append(("Query validation (invalid)", True, "✓ Invalid query rejected"))
            else:
                checks.append(("Query validation (invalid)", False, "✗ Invalid query accepted"))
            
        except Exception as e:
            checks.append(("Data pipeline", False, f"✗ Error: {e}"))
        
        # Print results
        for name, success, message in checks:
            if success:
                self.logger.info(f"  {message}")
            else:
                self.logger.error(f"  {message}")
        
        return all(success for _, success, _ in checks)
    
    async def _validate_api(self) -> bool:
        """Validate API functionality."""
        
        try:
            from sensei.api.v2.main import app
            client = TestClient(app)
            
            checks = []
            
            # Test health endpoint
            try:
                response = client.get("/health")
                if response.status_code == 200:
                    checks.append(("Health endpoint", True, "✓ Health endpoint accessible"))
                else:
                    checks.append(("Health endpoint", False, f"✗ Health endpoint returned {response.status_code}"))
            except Exception as e:
                checks.append(("Health endpoint", False, f"✗ Health endpoint error: {e}"))
            
            # Test OpenAPI docs
            try:
                response = client.get("/docs")
                if response.status_code in [200, 404]:  # 404 is OK if docs disabled
                    checks.append(("API docs", True, "✓ API docs endpoint accessible"))
                else:
                    checks.append(("API docs", False, f"✗ API docs returned {response.status_code}"))
            except Exception as e:
                checks.append(("API docs", False, f"✗ API docs error: {e}"))
            
            # Print results
            for name, success, message in checks:
                if success:
                    self.logger.info(f"  {message}")
                else:
                    self.logger.error(f"  {message}")
            
            return all(success for _, success, _ in checks)
            
        except Exception as e:
            self.logger.error(f"API validation error: {e}")
            return False
    
    def _validate_performance(self) -> bool:
        """Validate performance benchmarks."""
        
        checks = []
        
        try:
            # Test model creation speed
            start_time = time.time()
            model = ConversionModelV2()
            creation_time = time.time() - start_time
            
            if creation_time < 5.0:  # Should create model in < 5 seconds
                checks.append(("Model creation speed", True, f"✓ Model created in {creation_time:.2f}s"))
            else:
                checks.append(("Model creation speed", False, f"✗ Model creation took {creation_time:.2f}s"))
            
            # Test memory usage
            import psutil
            import os
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            if memory_mb < 1000:  # Should use < 1GB RAM
                checks.append(("Memory usage", True, f"✓ Memory usage: {memory_mb:.1f}MB"))
            else:
                checks.append(("Memory usage", False, f"⚠ High memory usage: {memory_mb:.1f}MB"))
            
        except Exception as e:
            checks.append(("Performance", False, f"✗ Error: {e}"))
        
        # Print results
        for name, success, message in checks:
            if success:
                self.logger.info(f"  {message}")
            else:
                self.logger.warning(f"  {message}")
        
        return all(success for _, success, _ in checks)
    
    def _print_summary(self):
        """Print validation summary."""
        
        self.logger.info("\n" + "="*60)
        self.logger.info("VALIDATION SUMMARY")
        self.logger.info("="*60)
        
        total_categories = len(self.results)
        passed_categories = sum(1 for result in self.results.values() if result["success"])
        
        for category, result in self.results.items():
            status = "✅ PASSED" if result["success"] else "❌ FAILED"
            self.logger.info(f"{category.upper():15} {status}")
            
            if not result["success"] and "error" in result:
                self.logger.info(f"                Error: {result['error']}")
        
        self.logger.info("-"*60)
        self.logger.info(f"OVERALL: {passed_categories}/{total_categories} categories passed")
        
        if passed_categories == total_categories:
            self.logger.info("🎉 ALL VALIDATIONS PASSED - Sensei AI Suite v2.0 is ready!")
        else:
            self.logger.error("❌ SOME VALIDATIONS FAILED - Please fix issues before deployment")
        
        self.logger.info("="*60)


def main():
    """Main validation function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Validate Sensei AI Suite v2.0")
    parser.add_argument(
        "--categories",
        nargs="+",
        choices=["system", "configuration", "models", "data_pipeline", "api", "performance"],
        help="Validation categories to run"
    )
    parser.add_argument(
        "--quick",
        action="store_true",
        help="Run quick validation (skip performance tests)"
    )
    
    args = parser.parse_args()
    
    categories = args.categories
    if args.quick and categories is None:
        categories = ["system", "configuration", "models", "api"]
    
    # Run validation
    validator = ValidationSuite()
    success = validator.run_validation(categories)
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
