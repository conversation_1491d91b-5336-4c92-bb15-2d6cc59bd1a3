#!/usr/bin/env python3
"""
Production training script for Sensei AI Suite.

Connects to real BigQuery data and trains models with full pipeline.
"""

import argparse
import json
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
import xgboost as xgb
import catboost as cb

# Import configuration
try:
    from config.settings import settings, ServerCapacity
except ImportError:
    print("Warning: Could not import settings, using defaults")
    settings = None

    # Define ServerCapacity locally if import fails
    from enum import Enum
    class ServerCapacity(str, Enum):
        SMALL = "small"
        MEDIUM = "medium"
        LARGE = "large"
        XLARGE = "xlarge"


def log_info(message: str, **kwargs):
    """Enhanced logging function."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    extra = " ".join([f"{k}={v}" for k, v in kwargs.items()])
    print(f"[{timestamp}] INFO: {message} {extra}")


def log_error(message: str, **kwargs):
    """Enhanced error logging function."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    extra = " ".join([f"{k}={v}" for k, v in kwargs.items()])
    print(f"[{timestamp}] ERROR: {message} {extra}")


def load_bigquery_data(query: str, max_samples: Optional[int] = None) -> pd.DataFrame:
    """
    Load data from BigQuery.
    
    Args:
        query: SQL query to execute
        max_samples: Maximum number of samples to load
        
    Returns:
        DataFrame with loaded data
    """
    try:
        from google.cloud import bigquery
        
        # Initialize BigQuery client
        if settings:
            project_id = settings.database.project_id
        else:
            project_id = "datalake-sensei"
            
        client = bigquery.Client(project=project_id)
        
        # Add LIMIT if max_samples specified
        if max_samples:
            if "LIMIT" not in query.upper():
                query += f" LIMIT {max_samples}"
        
        log_info("Executing BigQuery query", project=project_id)
        
        # Execute query
        df = client.query(query).to_dataframe()
        
        log_info("Data loaded from BigQuery", rows=len(df), columns=len(df.columns))
        return df
        
    except ImportError:
        log_error("google-cloud-bigquery not installed, using synthetic data")
        return create_synthetic_data(max_samples or 1000)
    except Exception as e:
        log_error(f"BigQuery error: {e}, falling back to synthetic data")
        return create_synthetic_data(max_samples or 1000)


def create_synthetic_data(n_samples: int = 1000) -> pd.DataFrame:
    """Create realistic synthetic data for testing."""
    log_info("Creating synthetic data", samples=n_samples)
    
    np.random.seed(42)  # For reproducibility
    
    data = pd.DataFrame({
        # Prospect identifiers
        'id_prospect': [f"PROSPECT_{i:06d}" for i in range(n_samples)],
        'email': [f"user{i}@company{i%100}.com" for i in range(n_samples)],
        'nom': [f"User {i}" for i in range(n_samples)],
        
        # Behavioral features
        'nb_interactions': np.random.poisson(5, n_samples),
        'hubspot_score': np.random.randint(0, 100, n_samples),
        'nb_jours_actifs': np.random.poisson(10, n_samples),
        'duree_moyenne_appels': np.random.exponential(120, n_samples),
        
        # Categorical features
        'response_speed': np.random.choice(['slow', 'medium', 'fast'], n_samples, p=[0.3, 0.5, 0.2]),
        'declared_budget': np.random.choice(['small', 'medium', 'large'], n_samples, p=[0.4, 0.4, 0.2]),
        'activity_sector': np.random.choice(['tech', 'finance', 'retail', 'other'], n_samples, p=[0.3, 0.2, 0.2, 0.3]),
        
        # Communication features
        'nb_appels_historique': np.random.poisson(3, n_samples),
        'nb_appels_matin': np.random.poisson(1, n_samples),
        'nb_appels_apres_midi': np.random.poisson(2, n_samples),
        'has_phone': np.random.choice([0, 1], n_samples, p=[0.1, 0.9]),
        'has_email': np.random.choice([0, 1], n_samples, p=[0.05, 0.95]),
        
        # Targets
        'y_converted_90j': np.random.choice([0, 1], n_samples, p=[0.85, 0.15]),  # 15% conversion
        'channel_timing_optimal': np.random.choice(
            ['email_morning', 'email_afternoon', 'call_morning', 'call_afternoon', 'call_evening'],
            n_samples, p=[0.2, 0.3, 0.25, 0.15, 0.1]
        )
    })
    
    return data


def prepare_conversion_features(data: pd.DataFrame) -> tuple:
    """Prepare features for conversion prediction."""
    # Select relevant features
    feature_cols = [
        'nb_interactions', 'hubspot_score', 'nb_jours_actifs', 'duree_moyenne_appels',
        'nb_appels_historique', 'nb_appels_matin', 'nb_appels_apres_midi'
    ]
    
    # Add categorical features (one-hot encoded)
    categorical_features = ['response_speed', 'declared_budget', 'activity_sector']
    
    features = data[feature_cols].copy()
    
    # One-hot encode categorical features
    for cat_col in categorical_features:
        if cat_col in data.columns:
            dummies = pd.get_dummies(data[cat_col], prefix=cat_col)
            features = pd.concat([features, dummies], axis=1)
    
    # Target
    target = data['y_converted_90j'] if 'y_converted_90j' in data.columns else np.random.choice([0, 1], len(data), p=[0.85, 0.15])
    
    log_info("Conversion features prepared", features=len(features.columns), samples=len(features))
    return features, pd.Series(target)


def prepare_channel_features(data: pd.DataFrame) -> tuple:
    """Prepare features for channel optimization."""
    # Select relevant features
    feature_cols = [
        'nb_appels_historique', 'nb_appels_matin', 'nb_appels_apres_midi',
        'duree_moyenne_appels', 'has_phone', 'has_email', 'nb_interactions'
    ]
    
    features = data[feature_cols].copy()
    
    # Add response rate feature (synthetic)
    features['response_rate'] = np.random.uniform(0, 1, len(features))
    
    # Target
    target = data['channel_timing_optimal'] if 'channel_timing_optimal' in data.columns else np.random.choice(
        ['email_morning', 'email_afternoon', 'call_morning', 'call_afternoon', 'call_evening'],
        len(data), p=[0.2, 0.3, 0.25, 0.15, 0.1]
    )
    
    log_info("Channel features prepared", features=len(features.columns), samples=len(features))
    return features, pd.Series(target)


def train_conversion_model(X: pd.DataFrame, y: pd.Series, capacity: ServerCapacity) -> Dict[str, Any]:
    """Train conversion model with production settings."""
    log_info("Training conversion model", samples=len(X), features=len(X.columns), capacity=capacity.value)
    
    start_time = time.time()
    
    # Split data
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # Configure model based on capacity
    if capacity == ServerCapacity.SMALL:
        params = {
            'n_estimators': 50,
            'max_depth': 3,
            'learning_rate': 0.1,
            'reg_alpha': 1.0,
            'reg_lambda': 1.0
        }
    elif capacity == ServerCapacity.MEDIUM:
        params = {
            'n_estimators': 100,
            'max_depth': 5,
            'learning_rate': 0.05,
            'reg_alpha': 0.5,
            'reg_lambda': 0.5
        }
    else:  # LARGE or XLARGE
        params = {
            'n_estimators': 200,
            'max_depth': 7,
            'learning_rate': 0.01,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1
        }
    
    # Train model
    model = xgb.XGBClassifier(
        **params,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42,
        eval_metric='auc'
    )
    
    model.fit(
        X_train, y_train,
        eval_set=[(X_val, y_val)],
        early_stopping_rounds=20,
        verbose=False
    )
    
    # Calculate metrics
    train_pred = model.predict_proba(X_train)[:, 1]
    val_pred = model.predict_proba(X_val)[:, 1]
    
    # Handle single class case
    if len(np.unique(y_train)) == 2:
        train_auc = roc_auc_score(y_train, train_pred)
    else:
        train_auc = 0.5
        
    if len(np.unique(y_val)) == 2:
        val_auc = roc_auc_score(y_val, val_pred)
    else:
        val_auc = 0.5
    
    # Cross-validation
    if len(np.unique(y)) == 2:
        cv_scores = cross_val_score(model, X, y, cv=3, scoring='roc_auc')
        cv_auc = cv_scores.mean()
    else:
        cv_auc = 0.5
    
    training_time = time.time() - start_time
    
    metrics = {
        'training_time_seconds': training_time,
        'train_size': len(X_train),
        'val_size': len(X_val),
        'feature_count': len(X.columns),
        'train_auc': float(train_auc),
        'val_auc': float(val_auc),
        'cv_auc': float(cv_auc),
        'overfitting_gap': float(train_auc - val_auc),
        'n_estimators_used': model.best_iteration if hasattr(model, 'best_iteration') else params['n_estimators'],
        'model_params': params
    }
    
    log_info("Conversion model trained", **{k: v for k, v in metrics.items() if isinstance(v, (int, float, str))})
    return metrics, model


def train_channel_model(X: pd.DataFrame, y: pd.Series, capacity: ServerCapacity) -> Dict[str, Any]:
    """Train channel model with production settings."""
    log_info("Training channel model", samples=len(X), features=len(X.columns), capacity=capacity.value)
    
    start_time = time.time()
    
    # Split data
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # Configure model based on capacity
    if capacity == ServerCapacity.SMALL:
        params = {
            'iterations': 50,
            'depth': 2,
            'learning_rate': 0.1,
            'l2_leaf_reg': 10.0
        }
    elif capacity == ServerCapacity.MEDIUM:
        params = {
            'iterations': 100,
            'depth': 4,
            'learning_rate': 0.05,
            'l2_leaf_reg': 5.0
        }
    else:  # LARGE or XLARGE
        params = {
            'iterations': 200,
            'depth': 6,
            'learning_rate': 0.01,
            'l2_leaf_reg': 1.0
        }
    
    # Train model
    model = cb.CatBoostClassifier(
        **params,
        random_strength=1.0,
        bagging_temperature=1.0,
        random_state=42,
        verbose=False,
        early_stopping_rounds=20
    )
    
    model.fit(
        X_train, y_train,
        eval_set=(X_val, y_val),
        use_best_model=True
    )
    
    # Calculate metrics
    train_pred = model.predict(X_train)
    val_pred = model.predict(X_val)
    
    train_acc = accuracy_score(y_train, train_pred)
    val_acc = accuracy_score(y_val, val_pred)
    
    # Cross-validation
    cv_scores = cross_val_score(model, X, y, cv=3, scoring='accuracy')
    cv_acc = cv_scores.mean()
    
    training_time = time.time() - start_time
    
    metrics = {
        'training_time_seconds': training_time,
        'train_size': len(X_train),
        'val_size': len(X_val),
        'feature_count': len(X.columns),
        'target_classes': len(np.unique(y)),
        'train_accuracy': float(train_acc),
        'val_accuracy': float(val_acc),
        'cv_accuracy': float(cv_acc),
        'overfitting_gap': float(train_acc - val_acc),
        'iterations_used': model.tree_count_,
        'model_params': params
    }
    
    log_info("Channel model trained", **{k: v for k, v in metrics.items() if isinstance(v, (int, float, str))})
    return metrics, model


def save_model_and_results(model_name: str, model, metrics: Dict[str, Any], capacity: ServerCapacity) -> None:
    """Save model and results to registry."""
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    # Create model-specific directory
    model_dir = models_dir / model_name
    model_dir.mkdir(exist_ok=True)
    
    # Generate version
    version = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save model
    model_path = model_dir / f"{model_name}_model_{version}.pkl"
    import joblib
    joblib.dump(model, model_path)
    
    # Load existing registry
    registry_path = models_dir / "registry.json"
    if registry_path.exists():
        with open(registry_path, 'r') as f:
            registry = json.load(f)
    else:
        registry = {}
    
    # Add new results
    if model_name not in registry:
        registry[model_name] = []
    
    registry[model_name].append({
        "version": version,
        "path": str(model_path),
        "metrics": metrics,
        "created_at": datetime.now().isoformat(),
        "server_capacity": capacity.value,
        "type": "production"
    })
    
    # Save registry
    with open(registry_path, 'w') as f:
        json.dump(registry, f, indent=2)
    
    log_info(f"Model saved", model=model_name, version=version, path=str(model_path))


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="Production training for Sensei AI Suite")
    parser.add_argument("--capacity", choices=["small", "medium", "large", "xlarge"], 
                       default="medium", help="Server capacity")
    parser.add_argument("--models", nargs="+", choices=["conversion", "channel"], 
                       default=["conversion", "channel"], help="Models to train")
    parser.add_argument("--max-samples", type=int, help="Maximum samples to load")
    parser.add_argument("--use-bigquery", action="store_true", help="Use real BigQuery data")
    
    args = parser.parse_args()
    
    # Get capacity
    try:
        capacity = ServerCapacity(args.capacity)
    except ValueError:
        capacity = ServerCapacity.MEDIUM
    
    log_info("Starting production training", 
             capacity=capacity.value, models=args.models, max_samples=args.max_samples)
    
    # Load data
    if args.use_bigquery:
        # This would contain real BigQuery queries
        query = """
        SELECT * FROM `datalake-sensei.sensei_data.vw_dim_contact`
        WHERE created_date >= '2024-01-01'
        """
        data = load_bigquery_data(query, args.max_samples)
    else:
        data = create_synthetic_data(args.max_samples or 5000)
    
    results = {}
    
    # Train conversion model
    if "conversion" in args.models:
        try:
            X, y = prepare_conversion_features(data)
            metrics, model = train_conversion_model(X, y, capacity)
            save_model_and_results("conversion", model, metrics, capacity)
            results["conversion"] = {"status": "success", "metrics": metrics}
        except Exception as e:
            log_error(f"Conversion model failed: {e}")
            results["conversion"] = {"status": "error", "error": str(e)}
    
    # Train channel model
    if "channel" in args.models:
        try:
            X, y = prepare_channel_features(data)
            metrics, model = train_channel_model(X, y, capacity)
            save_model_and_results("channel", model, metrics, capacity)
            results["channel"] = {"status": "success", "metrics": metrics}
        except Exception as e:
            log_error(f"Channel model failed: {e}")
            results["channel"] = {"status": "error", "error": str(e)}
    
    # Print summary
    print("\n" + "="*80)
    print("PRODUCTION TRAINING SUMMARY")
    print("="*80)
    print(f"Capacity: {capacity.value}")
    print(f"Data samples: {len(data)}")
    print(f"BigQuery: {'Yes' if args.use_bigquery else 'No (synthetic)'}")
    print("-"*80)
    
    for model_name, result in results.items():
        status = result.get("status", "unknown")
        print(f"{model_name:15} | {status.upper():10}")
        
        if status == "success":
            metrics = result.get("metrics", {})
            if "val_auc" in metrics:
                print(f"{'':15} | AUC: {metrics['val_auc']:.3f} (gap: {metrics.get('overfitting_gap', 0):.3f})")
            if "val_accuracy" in metrics:
                print(f"{'':15} | Accuracy: {metrics['val_accuracy']:.3f} (gap: {metrics.get('overfitting_gap', 0):.3f})")
            print(f"{'':15} | Training time: {metrics.get('training_time_seconds', 0):.1f}s")
    
    print("="*80)
    
    # Return success if all models trained successfully
    success = all(r.get("status") == "success" for r in results.values())
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
