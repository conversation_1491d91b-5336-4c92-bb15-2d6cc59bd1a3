#!/usr/bin/env python3
"""
Test basique d'import pour Sensei AI Suite v2.0
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_basic_imports():
    """Test des imports de base."""

    print("🧪 Testing Sensei AI Suite v2.0 basic imports...")

    try:
        # Test direct module imports (without dependencies)
        print("  📦 Testing core modules...")

        # Test if files exist and are importable
        import importlib.util

        # Test exceptions module
        spec = importlib.util.spec_from_file_location("exceptions", "src/sensei/core/exceptions.py")
        if spec and spec.loader:
            print("    ✅ Exceptions module found")

        # Test interfaces module
        spec = importlib.util.spec_from_file_location("interfaces", "src/sensei/core/interfaces.py")
        if spec and spec.loader:
            print("    ✅ Interfaces module found")

        print("🎉 Core modules structure validated!")
        print("  ⚠️  Note: Full validation requires dependencies (pip install -r requirements.txt)")
        return True

    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False

def test_structure():
    """Test de la structure des fichiers."""
    
    print("\n📁 Testing file structure...")
    
    required_files = [
        "src/sensei/__init__.py",
        "src/sensei/core/__init__.py",
        "src/sensei/api/v2/__init__.py",
        "src/sensei/models/__init__.py",
        "src/sensei/data/__init__.py",
        "src/sensei/features/__init__.py",
        "train.py",
        "validate.py",
        "Dockerfile",
        "docker-compose.yml",
        "requirements.txt",
        "README.md",
        "ARCHITECTURE.md",
        "DEPLOYMENT.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"    ✅ {file_path}")
    
    if missing_files:
        print(f"\n❌ Missing files: {missing_files}")
        return False
    else:
        print("\n🎉 All required files present!")
        return True

def main():
    """Fonction principale."""
    
    print("=" * 60)
    print("🧠 SENSEI AI SUITE V2.0 - VALIDATION BASIQUE")
    print("=" * 60)
    
    # Test structure
    structure_ok = test_structure()
    
    # Test imports
    imports_ok = test_basic_imports()
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DE LA VALIDATION")
    print("=" * 60)
    
    if structure_ok and imports_ok:
        print("🎉 VALIDATION RÉUSSIE - Sensei AI Suite v2.0 est prêt!")
        print("📝 Prochaines étapes:")
        print("   1. Installer les dépendances: pip install -r requirements.txt")
        print("   2. Configurer les credentials GCP")
        print("   3. Lancer la validation complète: python validate.py")
        print("   4. Entraîner les modèles: python train.py")
        print("   5. Démarrer l'API: uvicorn src.sensei.api.v2.main:app")
        return True
    else:
        print("❌ VALIDATION ÉCHOUÉE - Veuillez corriger les erreurs")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
