# Sensei AI Suite v2.0 - Development Docker Compose
version: '3.8'

services:
  # Main API service
  sensei-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      args:
        BUILD_DATE: ${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
        VERSION: ${VERSION:-2.0.0}
        VCS_REF: ${VCS_REF:-$(git rev-parse --short HEAD)}
    container_name: sensei-api-v2
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=INFO
      - GCP_PROJECT_ID=${GCP_PROJECT_ID:-datalake-sensei}
      - REDIS_URL=redis://redis:6379
      - API_WORKERS=1
      - API_RELOAD=true
    volumes:
      - ./src:/app/src:ro
      - ./models:/app/models
      - ./data:/app/data
      - ./logs:/app/logs
      - ./credentials:/app/credentials:ro
    depends_on:
      - redis
    networks:
      - sensei-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis cache
  redis:
    image: redis:7-alpine
    container_name: sensei-redis-v2
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - sensei-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru

  # Prometheus monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: sensei-prometheus-v2
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - sensei-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: sensei-grafana-v2
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - sensei-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # Jupyter notebook for development
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder
    container_name: sensei-jupyter-v2
    ports:
      - "8888:8888"
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=sensei-dev-token
    volumes:
      - ./src:/app/src
      - ./notebooks:/app/notebooks
      - ./data:/app/data
      - ./models:/app/models
    networks:
      - sensei-network
    restart: unless-stopped
    command: jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --notebook-dir=/app

  # Model training service
  trainer:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: sensei-trainer-v2
    environment:
      - ENVIRONMENT=development
      - GCP_PROJECT_ID=${GCP_PROJECT_ID:-datalake-sensei}
    volumes:
      - ./src:/app/src:ro
      - ./models:/app/models
      - ./data:/app/data
      - ./logs:/app/logs
      - ./credentials:/app/credentials:ro
    networks:
      - sensei-network
    profiles:
      - training
    command: python train_v2.py --models conversion_v2 channel_v2 nlp_v2 --max-samples 1000

volumes:
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  sensei-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
